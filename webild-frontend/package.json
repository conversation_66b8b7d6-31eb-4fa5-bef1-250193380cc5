{"name": "webild-front-inside", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.30.0", "gsap": "^3.13.0", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "eslint": "^9.32.0", "eslint-config-next": "15.4.3", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}}