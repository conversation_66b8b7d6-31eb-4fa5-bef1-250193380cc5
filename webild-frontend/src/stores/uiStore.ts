import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ViewType } from '@/types/chat';

interface UIState {
    activeView: ViewType;
    previewDevice: 'desktop' | 'tablet' | 'mobile';
    isSidebarOpen: boolean;
    isPreviewExpanded: boolean;
    showCloneView: boolean;
    hasTransitioned: boolean;
    allTasksCompleted: boolean;
    previewStage: number;
    lastCompletedMessageId: string | null;
    bobResponseCount: number;
    pendingBobMode: boolean;
    isBobModeActive: boolean;
    
    setActiveView: (view: ViewType) => void;
    setPreviewDevice: (device: 'desktop' | 'tablet' | 'mobile') => void;
    toggleSidebar: () => void;
    setSidebarOpen: (open: boolean) => void;
    togglePreviewExpanded: () => void;
    setPreviewExpanded: (expanded: boolean) => void;
    setShowCloneView: (show: boolean) => void;
    setHasTransitioned: (transitioned: boolean) => void;
    handleAllTasksCompleted: (messageId?: string) => void;
    handleResetPreview: () => void;
    incrementBobResponseCount: () => void;
    resetBobResponseCount: () => void;
    setPendingBobMode: (pending: boolean) => void;
    setIsBobModeActive: (active: boolean) => void;
}

export const useUIStore = create<UIState>()(
    devtools(
        (set, get) => ({
            activeView: 'bilder',
            previewDevice: 'desktop',
            isSidebarOpen: true,
            isPreviewExpanded: false,
            showCloneView: false,
            hasTransitioned: false,
            allTasksCompleted: false,
            previewStage: 0,
            lastCompletedMessageId: null,
            bobResponseCount: 0,
            pendingBobMode: false,
            isBobModeActive: false,
            
            setActiveView: (view) => set({ activeView: view }),
            setPreviewDevice: (device) => set({ previewDevice: device }),
            toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
            setSidebarOpen: (open) => set({ isSidebarOpen: open }),
            togglePreviewExpanded: () => set((state) => ({ isPreviewExpanded: !state.isPreviewExpanded })),
            setPreviewExpanded: (expanded) => set({ isPreviewExpanded: expanded }),
            setShowCloneView: (show) => set({ showCloneView: show }),
            setHasTransitioned: (transitioned) => set({ hasTransitioned: transitioned }),
            
            handleAllTasksCompleted: (messageId) => {
                const { lastCompletedMessageId } = get();
                if (messageId && messageId === lastCompletedMessageId) {
                    return;
                }
                set((state) => ({
                    allTasksCompleted: true,
                    previewStage: state.previewStage + 1,
                    lastCompletedMessageId: messageId || null
                }));
            },
            
            handleResetPreview: () => set({ allTasksCompleted: false }),
            
            incrementBobResponseCount: () => set((state) => ({ bobResponseCount: state.bobResponseCount + 1 })),
            resetBobResponseCount: () => set({ bobResponseCount: 0 }),
            setPendingBobMode: (pending) => set({ pendingBobMode: pending }),
            setIsBobModeActive: (active) => set({ isBobModeActive: active })
        }),
        {
            name: 'ui-store'
        }
    )
);