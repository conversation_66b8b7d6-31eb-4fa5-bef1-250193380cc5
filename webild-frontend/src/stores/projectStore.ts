import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ProjectData } from '@/types/project';
import { getProject, saveProject, deleteProject } from '@/utils/projectStorage';
import { createProjectAPI, getProjectIDAPI, ApiError } from '@/utils/api';

interface ProjectState {
    currentProject: ProjectData | null;
    projects: ProjectData[];
    isLoading: boolean;
    error: string | null;

    setCurrentProject: (project: ProjectData) => void;
    updateProject: (id: string, updates: Partial<ProjectData>) => void;
    loadProject: (id: string, getToken?: () => Promise<string | null>) => Promise<void>;
    createProject: (data: Omit<ProjectData, 'id' | 'createdAt'>, getToken: () => Promise<string | null>) => Promise<ProjectData>;
    removeProject: (id: string) => void;
    clearError: () => void;
}

export const useProjectStore = create<ProjectState>()(
    devtools(
        (set) => ({
            currentProject: null,
            projects: [],
            isLoading: false,
            error: null,
            
            setCurrentProject: (project) => set({ currentProject: project }),
            
            updateProject: (id, updates) => set((state) => ({
                projects: state.projects.map(p => 
                    p.id === id ? { ...p, ...updates } : p
                ),
                currentProject: state.currentProject?.id === id 
                    ? { ...state.currentProject, ...updates }
                    : state.currentProject
            })),
            
            loadProject: async (id, getToken) => {
                set({ isLoading: true, error: null });

                try {
                    // First try to load from local storage for quick access
                    const localProject = getProject(id);

                    // If we have a token, validate with backend
                    if (getToken) {
                        try {
                            const token = await getToken();

                            if (token) {
                                // Validate project exists and user has access
                                const backendResponse = await getProjectIDAPI(id, token);

                                // Convert backend response to frontend format
                                const validatedProject: ProjectData = {
                                    id: backendResponse.data.id,
                                    mode: localProject?.mode || 'build', // Use local mode if available, fallback to 'build'
                                    prompt: localProject?.prompt || backendResponse.data.name, // Use local prompt or project name
                                    createdAt: backendResponse.data.createdAt
                                };

                                // Save to local storage and update state
                                saveProject(validatedProject);
                                set({ currentProject: validatedProject, isLoading: false });
                                return;
                            }
                        } catch (error) {
                            // Handle specific API errors
                            if (error instanceof ApiError) {
                                if (error.status === 404) {
                                    set({ error: 'Invalid Project', isLoading: false });
                                    return;
                                } else if (error.status === 403 || error.status === 401) {
                                    set({ error: 'You don\'t have access to this project', isLoading: false });
                                    return;
                                }
                            }
                            // For other errors, fall back to local project if available
                        }
                    }

                    // Fallback to local project if backend validation fails or no token
                    if (localProject) {
                        set({ currentProject: localProject, isLoading: false });
                    } else {
                        set({ error: 'Invalid Project', isLoading: false });
                    }
                } catch {
                    set({ error: 'Failed to load project', isLoading: false });
                }
            },
            
            createProject: async (data, getToken) => {
                set({ isLoading: true, error: null });

                try {
                    const token = await getToken();

                    if (!token) {
                        throw new Error("Authentication token not found");
                    }

                    // Call the backend API to create the project
                    const response = await createProjectAPI({
                        name: data.prompt // Use the prompt as the project name
                    }, token);

                    // Convert backend project to frontend project data
                    const project: ProjectData = {
                        id: response.data.id,
                        mode: data.mode,
                        prompt: data.prompt,
                        createdAt: response.data.createdAt
                    };

                    // Save to local storage and update state
                    saveProject(project);
                    set((state) => ({
                        projects: [...state.projects, project],
                        currentProject: project,
                        isLoading: false
                    }));

                    return project;
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : "Failed to create project";
                    set({ error: errorMessage, isLoading: false });
                    throw error;
                }
            },
            
            removeProject: (id) => {
                deleteProject(id);
                set((state) => ({
                    projects: state.projects.filter(p => p.id !== id),
                    currentProject: state.currentProject?.id === id ? null : state.currentProject
                }));
            },
            
            clearError: () => set({ error: null })
        }),
        {
            name: 'project-store'
        }
    )
);