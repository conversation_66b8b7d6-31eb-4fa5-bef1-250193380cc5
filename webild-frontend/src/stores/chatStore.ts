import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ChatMessage, IframeContent } from '@/types/chat';
import { AIResponse } from '@/types/aiResponse';
import { ANIMATION_CONSTANTS } from '@/constants/animation';

interface ChatState {
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    responseIndex: number;
    iframeCompletionTimers: Map<string, NodeJS.Timeout>;
    responseTimeout: NodeJS.Timeout | null;
    
    addMessage: (message: ChatMessage) => void;
    updateMessage: (id: string, updates: Partial<ChatMessage>) => void;
    setMessages: (messages: ChatMessage[]) => void;
    clearMessages: () => void;
    
    sendMessage: (content: string, responses: AIResponse[], skipDelay?: boolean, onPreviewReset?: () => void) => Promise<void>;
    handleAnimationComplete: (messageId: string) => void;
    handleStopGeneration: () => void;
    startIframeCompletion: (messageId: string) => void;
    
    setLoading: (loading: boolean) => void;
    setResponseReady: (ready: boolean) => void;
    incrementResponseIndex: () => void;
}

export const useChatStore = create<ChatState>()(
    devtools(
        (set, get) => ({
            messages: [],
            isLoading: false,
            isResponseReady: false,
            responseIndex: 0,
            iframeCompletionTimers: new Map(),
            responseTimeout: null,
            
            addMessage: (message) => set((state) => ({
                messages: [...state.messages, message]
            })),
            
            updateMessage: (id, updates) => set((state) => ({
                messages: state.messages.map(msg =>
                    msg.id === id ? { ...msg, ...updates } : msg
                )
            })),
            
            setMessages: (messages) => set({ messages }),
            
            clearMessages: () => {
                const { iframeCompletionTimers } = get();
                iframeCompletionTimers.forEach(timer => clearTimeout(timer));
                set({ messages: [], responseIndex: 0, iframeCompletionTimers: new Map() });
            },
            
            sendMessage: async (content, responses, skipDelay = false, onPreviewReset) => {
                const { addMessage, updateMessage, incrementResponseIndex, isLoading, handleStopGeneration } = get();
                
                if (isLoading && !content.trim()) {
                    handleStopGeneration();
                    return;
                }
                
                if (!content.trim()) return;
                
                set({ isLoading: true });
                
                const userMessage: ChatMessage = {
                    id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                    role: "user",
                    content: content,
                    type: "text",
                    timestamp: new Date()
                };
                
                const aiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                const thinkingMessage: ChatMessage = {
                    id: aiMessageId,
                    role: "assistant",
                    content: "",
                    type: "text",
                    timestamp: new Date()
                };
                
                addMessage(userMessage);
                addMessage(thinkingMessage);
                
                const delay = skipDelay ? 0 : ANIMATION_CONSTANTS.AI_RESPONSE_DELAY;
                
                const timeout = setTimeout(() => {
                    const responseIdx = get().responseIndex;
                    const response = responses[responseIdx % responses.length];
                    
                    if (response.type === "tasks" && !response.showPreview && onPreviewReset) {
                        onPreviewReset();
                    }
                    
                    updateMessage(aiMessageId, {
                        content: response.content,
                        type: response.type,
                        showPreview: response.showPreview,
                        taskCompletionTimes: response.taskCompletionTimes,
                        animationStartTime: Date.now()
                    });
                    
                    if (response.type === 'iframe') {
                        get().startIframeCompletion(aiMessageId);
                    }
                    
                    incrementResponseIndex();
                    set({ isResponseReady: true, responseTimeout: null });
                }, delay);
                
                set({ responseTimeout: timeout });
            },
            
            handleAnimationComplete: (messageId) => {
                const { updateMessage } = get();
                updateMessage(messageId, { animationComplete: true });
                set({ isLoading: false, isResponseReady: false });
            },
            
            handleStopGeneration: () => {
                const { responseTimeout, messages, updateMessage, iframeCompletionTimers } = get();
                
                if (responseTimeout) {
                    clearTimeout(responseTimeout);
                }
                
                const lastMessage = messages[messages.length - 1];
                if (lastMessage?.role === 'assistant') {
                    if (lastMessage.content === '') {
                        set((state) => ({
                            messages: state.messages.slice(0, -1)
                        }));
                    } else if (lastMessage.type === 'iframe') {
                        const timer = iframeCompletionTimers.get(lastMessage.id);
                        if (timer) {
                            clearTimeout(timer);
                            iframeCompletionTimers.delete(lastMessage.id);
                        }
                        updateMessage(lastMessage.id, {
                            iframeCompleted: true,
                            animationComplete: true,
                            manuallyStopped: true
                        });
                    } else if (typeof lastMessage.content === 'string' && lastMessage.animationStartTime) {
                        const elapsedTime = Date.now() - lastMessage.animationStartTime;
                        const visibleChars = Math.floor(elapsedTime / ANIMATION_CONSTANTS.CHAR_DELAY);
                        const truncatedContent = lastMessage.content.substring(0, visibleChars);
                        
                        updateMessage(lastMessage.id, {
                            content: truncatedContent,
                            animationComplete: true
                        });
                    } else {
                        updateMessage(lastMessage.id, {
                            animationComplete: true
                        });
                    }
                }
                
                set({ isLoading: false, isResponseReady: false, responseTimeout: null });
            },
            
            startIframeCompletion: (messageId: string) => {
                const { messages, updateMessage, iframeCompletionTimers } = get();
                const message = messages.find(msg => msg.id === messageId);
                
                if (message?.type === 'iframe' && !message.iframeCompleted) {
                    const content = message.content as IframeContent;
                    const completionTime = content.completionTime || 3000;
                    
                    const existingTimer = iframeCompletionTimers.get(messageId);
                    if (existingTimer) {
                        clearTimeout(existingTimer);
                    }
                    
                    const timer = setTimeout(() => {
                        updateMessage(messageId, { iframeCompleted: true });
                        iframeCompletionTimers.delete(messageId);
                    }, completionTime);
                    
                    iframeCompletionTimers.set(messageId, timer);
                }
            },
            
            setLoading: (loading) => set({ isLoading: loading }),
            setResponseReady: (ready) => set({ isResponseReady: ready }),
            incrementResponseIndex: () => set((state) => ({ responseIndex: state.responseIndex + 1 }))
        }),
        {
            name: 'chat-store'
        }
    )
);