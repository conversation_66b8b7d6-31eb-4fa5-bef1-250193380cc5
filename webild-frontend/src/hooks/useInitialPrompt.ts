import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export const useInitialPrompt = () => {
    const searchParams = useSearchParams();
    const [initialPrompt, setInitialPrompt] = useState<string>("");

    useEffect(() => {
        const prompt = searchParams.get("prompt");
        if (prompt) {
            setInitialPrompt(decodeURIComponent(prompt));
        }
    }, [searchParams]);

    return initialPrompt;
};