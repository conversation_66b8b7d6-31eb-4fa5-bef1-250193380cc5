import { useState, useRef, useEffect } from "react";
import type { BrowseCategory } from "@/types/browse";

export const useVaultSidebar = (categories: BrowseCategory[]) => {
    const [expandedCategory, setExpandedCategory] = useState<string | null>(categories[0]?.id || null);
    const [selectedCategory, setSelectedCategory] = useState<string | null>(categories[0]?.id || null);
    const [selectedSubItem, setSelectedSubItem] = useState<string | null>(null);
    const contentRefs = useRef<Record<string, HTMLDivElement | null>>({});
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    const toggleCategory = (categoryId: string) => {
        if (expandedCategory !== categoryId) {
            setExpandedCategory(categoryId);
        }
    };

    const handleCategoryClick = (category: BrowseCategory, onItemSelect?: (categoryId: string, subItemId?: string) => void) => {
        setSelectedCategory(category.id);
        setSelectedSubItem(null);
        toggleCategory(category.id);
        onItemSelect?.(category.id);
    };

    const handleSubItemClick = (categoryId: string, subItemId: string, onItemSelect?: (categoryId: string, subItemId?: string) => void) => {
        setSelectedCategory(categoryId);
        setSelectedSubItem(subItemId);
        onItemSelect?.(categoryId, subItemId);
    };

    return {
        expandedCategory,
        selectedCategory,
        selectedSubItem,
        contentRefs,
        mounted,
        handleCategoryClick,
        handleSubItemClick
    };
};