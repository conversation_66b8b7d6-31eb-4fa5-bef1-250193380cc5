import { useEffect, useMemo } from "react";
import { ANIMATION_CONSTANTS } from "@/constants/animation";

interface UseTextAnimationProps {
    text: string;
    onComplete?: () => void;
    continuous?: boolean;
}

export const useTextAnimation = ({ text, onComplete, continuous }: UseTextAnimationProps) => {
    const lines = useMemo(() => 
        text.split('\n').filter(line => line.trim().length > 0)
    , [text]);
    
    const totalChars = useMemo(() => {
        let count = 0;
        lines.forEach(line => {
            const words = line.split(' ');
            words.forEach((word, index) => {
                count += word.length;
                if (index < words.length - 1) {
                    count += 1;
                }
            });
        });
        return count;
    }, [lines]);
    
    const colorIndices = useMemo(() => 
        new Set(
            [...Array(totalChars).keys()]
                .sort(() => Math.random() - 0.5)
                .slice(0, Math.floor(totalChars * 0.5))
        )
    , [totalChars]);

    useEffect(() => {
        if (onComplete && !continuous) {
            const lastCharStartTime = (totalChars - 1) * ANIMATION_CONSTANTS.CHAR_DELAY;
            const totalAnimationTime = lastCharStartTime + ANIMATION_CONSTANTS.CHAR_DURATION;
            const timer = setTimeout(onComplete, totalAnimationTime);
            return () => clearTimeout(timer);
        }
    }, [totalChars, onComplete, continuous]);

    return {
        lines,
        totalChars,
        colorIndices
    };
};