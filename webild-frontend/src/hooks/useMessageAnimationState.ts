import { useState, useEffect } from 'react';
import { ChatMessage } from '@/types/chat';
import { shouldAnimate } from '@/utils/chat';
import { ANIMATION_CONSTANTS } from '@/constants/animation';

export const useMessageAnimationState = (
    messages: ChatMessage[], 
    isLoading: boolean, 
    isResponseReady: boolean
) => {
    const [animatingMessageIds, setAnimatingMessageIds] = useState<Set<string>>(new Set());
    
    useEffect(() => {
        const timers: NodeJS.Timeout[] = [];
        
        messages.forEach((message, index) => {
            if (shouldAnimate(message, index, messages.length, isLoading, isResponseReady)) {
                setAnimatingMessageIds(prev => new Set(prev).add(message.id));
                
                const textLength = typeof message.content === 'string' ? message.content.length : 100;
                const estimatedDuration = textLength * ANIMATION_CONSTANTS.CHAR_DELAY + 
                    ANIMATION_CONSTANTS.CHAR_DURATION + 
                    ANIMATION_CONSTANTS.COMPLETION_BUFFER;
                
                const timer = setTimeout(() => {
                    setAnimatingMessageIds(prev => {
                        const next = new Set(prev);
                        next.delete(message.id);
                        return next;
                    });
                }, estimatedDuration);
                
                timers.push(timer);
            } else if (message.animationComplete) {
                // Immediately remove from animating set if animation is complete
                setAnimatingMessageIds(prev => {
                    const next = new Set(prev);
                    next.delete(message.id);
                    return next;
                });
            }
        });
        
        return () => {
            timers.forEach(timer => clearTimeout(timer));
        };
    }, [messages, isLoading, isResponseReady]);
    
    const isAnyMessageAnimating = messages.some((message, index) => 
        shouldAnimate(message, index, messages.length, isLoading, isResponseReady) && 
        animatingMessageIds.has(message.id)
    );
    
    return { isAnyMessageAnimating };
};