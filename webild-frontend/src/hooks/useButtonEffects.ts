import { useRef, useEffect, useCallback } from 'react';

export function useButtonEffects<T extends HTMLElement = HTMLButtonElement>() {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    audioRef.current = new Audio('/audio/click.mp3');
    audioRef.current.volume = 0.75;
  }, []);

  const playSound = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(() => {});
    }
  }, []);

  const handleMouseEnter = useCallback(() => {
    if (window.innerWidth > 768) {
      playSound();
    }
  }, [playSound]);

  const handleClick = useCallback((e: React.MouseEvent<T>, onClick?: (e: React.MouseEvent<T>) => void) => {
    playSound();
    if (onClick) {
      onClick(e);
    }
  }, [playSound]);

  return {
    handleMouseEnter,
    handleClick,
    buttonClassName: 'transition-all duration-200 hover:-translate-y-[3px]'
  };
}