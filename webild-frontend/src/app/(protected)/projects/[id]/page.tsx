"use client";

import CloningView from "@/components/widgets/projects/CloningView";
import <PERSON><PERSON>ie<PERSON> from "@/components/widgets/projects/BobView";
import BuildView from "@/components/widgets/projects/BuildView";
import { useProjectWorkspace } from "@/hooks/useProjectWorkspace";
import { ANIMATION_CONSTANTS } from "@/constants/animation";
import ProjectLoad from "@/components/widgets/projects/ProjectLoad";
import ProjectError from "@/components/widgets/projects/ProjectError";

const Page = ({ params }: { params: Promise<{ id: string }> }) => {
  const {
    projectData,
    projectError,
    projectIsLoading,
    shouldShowCloneView,
    shouldShowBobView,
    clonedUrl,
    activeView,
    setActiveView,
    allTasksCompleted,
    previewStage,
    chatContainerRef,
    cloningViewRef,
    messages,
    isLoading,
    isResponseReady,
    handleMessageSubmit,
    handleAnimationComplete,
    handleAllTasksCompleted,
    incrementBobResponseCount,
  } = useProjectWorkspace({ params });

  if (projectIsLoading) {
    return <ProjectLoad />;
  }

  if (projectError || !projectData) {
    return <ProjectError projectError={projectError} />;
  }

  return (
    <>
      {shouldShowCloneView ? (
        <CloningView
          ref={cloningViewRef}
          messages={messages}
          isLoading={isLoading}
          isResponseReady={isResponseReady}
          onSendMessage={handleMessageSubmit}
          onAnimationComplete={handleAnimationComplete}
          onAllTasksCompleted={handleAllTasksCompleted}
        />
      ) : shouldShowBobView ? (
        <BobView
          ref={cloningViewRef}
          messages={messages}
          isLoading={isLoading}
          isResponseReady={isResponseReady}
          onSendMessage={handleMessageSubmit}
          onAnimationComplete={(messageId) => {
            handleAnimationComplete(messageId);
            const message = messages.find((m) => m.id === messageId);
            if (message?.role === "assistant") {
              setTimeout(() => {
                incrementBobResponseCount();
              }, ANIMATION_CONSTANTS.COMPLETION_BUFFER);
            }
          }}
          onAllTasksCompleted={handleAllTasksCompleted}
        />
      ) : (
        <BuildView
          ref={chatContainerRef}
          activeView={activeView}
          setActiveView={setActiveView}
          messages={messages}
          isLoading={isLoading}
          isResponseReady={isResponseReady}
          onSendMessage={handleMessageSubmit}
          onAnimationComplete={handleAnimationComplete}
          onAllTasksCompleted={handleAllTasksCompleted}
          shouldShowPreview={allTasksCompleted || !!clonedUrl}
          previewStage={previewStage}
          clonedUrl={clonedUrl}
        />
      )}
    </>
  );
};

export default Page;
