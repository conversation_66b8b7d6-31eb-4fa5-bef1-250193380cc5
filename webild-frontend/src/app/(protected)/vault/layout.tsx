import { Metadata } from "next";

export const metadata: Metadata = {
    title: "Vault | Webild",
    description: "Secure storage for your web projects and assets",
    keywords: ["vault", "storage", "projects", "assets", "web builder"],
    openGraph: {
        title: "Vault | Webild",
        description: "Secure storage for your web projects and assets",
        type: "website",
    },
};

export default function VaultLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return <>{children}</>;
}