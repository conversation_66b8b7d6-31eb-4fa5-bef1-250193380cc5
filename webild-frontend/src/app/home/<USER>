"use client";

import AnimationContainer from "@/components/layout/AnimationContainer";
import About from "@/components/widgets/home/<USER>/About";
import Features from "@/components/widgets/home/<USER>/Features";
import Hero from "@/components/widgets/home/<USER>/Hero";
import { useUserStore } from "@/stores/userStore";
import { useAuth } from "@clerk/nextjs";
import { useCallback, useEffect } from "react";

const Page = () => {
  const { isSignedIn, getToken } = useAuth();
  const { user, fetchUser } = useUserStore();

  const handleFetchUser = useCallback(async () => {
    if (!isSignedIn) return;

    const token = await getToken({
      template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
    });

    if (token) {
      await fetchUser(() => Promise.resolve(token));
    }
  }, [isSignedIn, getToken, fetchUser]);

  useEffect(() => {
    if (isSignedIn && !user) {
      handleFetchUser();
    }
  }, [isSignedIn, user, handleFetchUser]);

  return (
    <div className="relative w-full h-fit">
      <AnimationContainer
        className="relative z-10 w-full h-fit flex flex-col gap-6"
        animationType="full"
      >
        <Hero />
        {!isSignedIn && (
          <>
            <About />
            <Features />
          </>
        )}
      </AnimationContainer>
    </div>
  );
};

export default Page;
