import type { Metadata } from 'next'
import { <PERSON><PERSON>rovider } from '@clerk/nextjs'
import { Inter_Tight } from 'next/font/google'
import './globals.css'
import Navigation from '@/components/layout/Navigation'

const interTight = Inter_Tight({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
})

export const metadata: Metadata = {
  title: 'Webild',
  description: '',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider
      appearance={{
        elements: {
          rootBox: {
            height: "auto",
            width: "auto",
          },
          card: {
            backgroundColor: "#FFFFFF",
            boxShadow: "none",
          },
          footer: {
            display: "none",
          },
          footerAction: {
            display: "none",
          },
        }
      }}
      signInFallbackRedirectUrl="/home"
      signUpFallbackRedirectUrl="/home"
      afterSignOutUrl="/home"
    >
      <html lang="en">
        <body className={`${interTight.className} antialiased`}>
          <Navigation />
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}