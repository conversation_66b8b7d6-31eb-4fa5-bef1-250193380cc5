"use client";

import { memo, useEffect, useState } from "react";
import { Check } from "lucide-react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import type { TasksContent } from "@/types/chat";

interface TasksResponseProps {
    content: TasksContent;
    isAnimating: boolean;
    onAnimationComplete: () => void;
    taskCompletionTimes?: number[];
    onAllTasksCompleted?: () => void;
}

const TasksResponse = memo(({ content, isAnimating, onAnimationComplete, taskCompletionTimes, onAllTasksCompleted }: TasksResponseProps) => {
    const [completedTasks, setCompletedTasks] = useState<number[]>([]);
    const [isPaused, setIsPaused] = useState(false);
    useEffect(() => {
        if (isAnimating && !taskCompletionTimes) {
            const timer = setTimeout(onAnimationComplete, 100);
            return () => clearTimeout(timer);
        }
    }, [isAnimating, onAnimationComplete, taskCompletionTimes]);

    useEffect(() => {
        if (!isAnimating) {
            setIsPaused(true);
            return;
        }
        
        setIsPaused(false);
        
        if (taskCompletionTimes && taskCompletionTimes.length > 0) {
            const timers: NodeJS.Timeout[] = [];
            
            content.tasks.forEach((_, index) => {
                if (!completedTasks.includes(index)) {
                    const timer = setTimeout(() => {
                        setCompletedTasks(prev => [...prev, index]);
                    }, taskCompletionTimes[index]);
                    timers.push(timer);
                }
            });
            
            const lastTaskTimer = setTimeout(() => {
                onAllTasksCompleted?.();
                onAnimationComplete();
            }, Math.max(...taskCompletionTimes));
            timers.push(lastTaskTimer);

            return () => {
                timers.forEach(timer => clearTimeout(timer));
            };
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAnimating, content.tasks.length, taskCompletionTimes?.join(','), onAllTasksCompleted, onAnimationComplete]);

    return (
        <AnimationContainer
            className="w-full p-5 flex flex-col gap-5 bg-off-white border border-white text-black rounded"
            animationType="fade"
        >
            <h3 className="text-sm">{content.title}</h3>
            
            <div className="flex flex-col gap-2">
                {content.tasks.map((task, index) => {
                    const isCompleted = completedTasks.includes(index);
                    return (
                        <div key={index} className="flex gap-3 items-center">
                            <div className="relative h-4 aspect-square flex items-center justify-center flex-shrink-0">
                                <AnimationContainer
                                    key={isCompleted ? 'completed' : 'loading'}
                                    className="absolute inset-0"
                                    animationType="fade"
                                >
                                    {isCompleted ? (
                                        <div className="h-full w-full green-box flex items-center justify-center rounded-full">
                                            <Check className="h-[0.55rem] w-auto text-white" strokeWidth={3} />
                                        </div>
                                    ) : (
                                        <div className={`h-full aspect-square rounded-full border border-white border-t-blue ${!isPaused ? 'animate-spin' : ''}`} />
                                    )}
                                </AnimationContainer>
                            </div>
                            <p className="text-sm">{task.text}</p>
                        </div>
                    );
                })}
            </div>
        </AnimationContainer>
    );
});

TasksResponse.displayName = "TasksResponse";

export default TasksResponse;