"use client";

import React, { memo } from "react";
import <PERSON><PERSON><PERSON> from "./AnimatedChar";
import { ANIMATION_CONSTANTS } from "@/constants/animation";

interface AnimatedLineProps {
    line: string;
    lineIndex: number;
    startCharIndex: number;
    colorIndices: Set<number>;
    instant?: boolean;
}

const AnimatedLine = memo(({ line, lineIndex, startCharIndex, colorIndices, instant = false }: AnimatedLineProps) => {
    const words = line.split(' ');
    const isListItem = /^(\d+\.|•|-)/.test(line.trim());
    const marginClass = lineIndex > 0 && !isListItem ? "mt-4" : "";
    
    let charIndex = startCharIndex;

    return (
        <p className={`m-0 ${marginClass}`}>
            {words.map((word, wordIndex) => {
                const wordChars = word.split('').map((char) => {
                    const currentIndex = charIndex++;
                    return (
                        <AnimatedChar
                            key={currentIndex}
                            char={char}
                            duration={ANIMATION_CONSTANTS.CHAR_DURATION}
                            colorTransition={colorIndices.has(currentIndex)}
                            colorDelay={ANIMATION_CONSTANTS.COLOR_DELAY}
                            startColor={ANIMATION_CONSTANTS.COLOR_START}
                            delay={instant ? 0 : currentIndex * ANIMATION_CONSTANTS.CHAR_DELAY}
                        />
                    );
                });

                if (wordIndex < words.length - 1) {
                    charIndex++;
                    return (
                        <React.Fragment key={`${lineIndex}-${wordIndex}`}>
                            <span className="inline">{wordChars}</span>
                            <span> </span>
                        </React.Fragment>
                    );
                }

                return (
                    <span key={`${lineIndex}-${wordIndex}`} className="inline">
                        {wordChars}
                    </span>
                );
            })}
        </p>
    );
});

AnimatedLine.displayName = "AnimatedLine";

export default AnimatedLine;