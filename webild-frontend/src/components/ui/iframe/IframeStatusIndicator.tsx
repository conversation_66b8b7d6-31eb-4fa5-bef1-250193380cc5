"use client";

import { memo } from "react";
import { Check } from "lucide-react";
import AnimationContainer from "@/components/layout/AnimationContainer";

interface IframeStatusIndicatorProps {
    isCompleted: boolean;
}

const IframeStatusIndicator = memo(({ isCompleted }: IframeStatusIndicatorProps) => {
    return (
        <div className="flex items-center gap-2">
            <div className="relative h-4 aspect-square flex items-center justify-center flex-shrink-0">
                <AnimationContainer
                    key={isCompleted ? 'completed' : 'loading'}
                    className="absolute inset-0"
                    animationType="fade"
                >
                    {isCompleted ? (
                        <div className="h-full w-full green-box flex items-center justify-center rounded-full">
                            <Check className="h-[0.55rem] w-auto text-white" strokeWidth={3} />
                        </div>
                    ) : (
                        <div className="h-full aspect-square rounded-full border border-white border-t-blue animate-spin" />
                    )}
                </AnimationContainer>
            </div>
            <p className="text-sm">{isCompleted ? 'Visited website vita-lenta.com' : 'Visting website vita-lenta.com'}</p>
        </div>
    );
});

IframeStatusIndicator.displayName = "IframeStatusIndicator";

export default IframeStatusIndicator;