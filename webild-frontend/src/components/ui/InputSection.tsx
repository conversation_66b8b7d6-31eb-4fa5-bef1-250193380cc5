"use client"

import <PERSON>ton from "@/components/ui/Button"
import AutoExpandTextarea from "@/components/ui/AutoExpandTextarea"
import AnimationContainer from "@/components/layout/AnimationContainer"
import { ArrowUp, Plus, Square } from "lucide-react"
import Image from "next/image"
import { strs } from "@/constants/pages/home/<USER>/strs"
import { useAuth } from "@clerk/nextjs"
import { useRouter } from "next/navigation"
import { useState, KeyboardEvent, useCallback, memo, useRef, useEffect } from "react"
import { InputSectionProps } from "@/types/components/InputSection"
import { useProjectStore } from "@/stores/projectStore"
import { useUIStore } from "@/stores/uiStore"

export default memo(function InputSection({
  containerClassName = "",
  leftButtonsClassName = "",
  rightButtonsClassName = "",
  onSubmit,
  textareaMaxLines,
  isLoading = false
}: InputSectionProps) {
  const { isSignedIn, getToken } = useAuth()
  const router = useRouter()
  const [inputValue, setInputValue] = useState("")
  const [isCloneMode, setIsCloneMode] = useState(false)
  const [isPlanMode, setIsPlanMode] = useState(false)
  const [isBobMode, setIsBobMode] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const { createProject } = useProjectStore()
  const { pendingBobMode, setPendingBobMode, isBobModeActive } = useUIStore()

  useEffect(() => {
    if (pendingBobMode) {
      setInputValue("Let's build a website together!")
      setIsCloneMode(false)
      setIsPlanMode(false)
      setIsBobMode(true)
      if (textareaRef.current) {
        textareaRef.current.focus()
        textareaRef.current.setSelectionRange(textareaRef.current.value.length, textareaRef.current.value.length)
      }
      setPendingBobMode(false)
    }
  }, [pendingBobMode, setPendingBobMode])

  useEffect(() => {
    if (!isBobModeActive && isBobMode) {
      setIsBobMode(false)
      setInputValue("")
    }
  }, [isBobModeActive, isBobMode])

  const handleSubmit = useCallback(() => {
    if (!isSignedIn) {
      router.push("/sign-in")
      return
    }

    if (onSubmit) {
      if (isLoading && inputValue.trim()) {
        onSubmit("");
        setTimeout(() => {
          onSubmit(inputValue);
          setInputValue("");
        }, 100);
      } else if (isLoading) {
        onSubmit("");
      } else if (inputValue.trim()) {
        onSubmit(inputValue);
        setInputValue("");
      }
    } else {
      console.log("Submitting input:", inputValue)

      const handleCreateProject = async () => {
        try {
          const token = await getToken({
            template: process.env.NEXT_PUBLIC_TEMPLATE_NAME,
          });

          if (!token) {
            throw new Error("Authentication token not found");
          }

          const project = await createProject({
            mode: isCloneMode ? 'clone' : isBobMode ? 'bob' : 'build',
            prompt: inputValue.trim()
          }, () => Promise.resolve(token));

          router.push(`/projects/${project.id}`);
        } catch (error) {
          console.error("Failed to create project:", error);
          // You might want to show an error message to the user here
        }
      };

      handleCreateProject();
    }
  }, [isSignedIn, router, onSubmit, inputValue, isCloneMode, isBobMode, createProject, isLoading, getToken])

  const handleKeyDown = useCallback((e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      if (!isLoading || inputValue.trim()) {
        handleSubmit()
      }
    }
  }, [handleSubmit, isLoading, inputValue])

  const handleCloneClick = useCallback(() => {
    setIsCloneMode(prev => !prev)
    setIsPlanMode(false)
    setIsBobMode(false)
    if (!isCloneMode) {
      setInputValue("Clone this website URL: ")
      if (textareaRef.current) {
        textareaRef.current.focus()
        const cursorPosition = "Clone this website URL: ".length
        textareaRef.current.setSelectionRange(cursorPosition, cursorPosition)
      }
    } else {
      setInputValue("")
    }
  }, [isCloneMode])

  const handlePlanClick = useCallback(() => {
    setIsPlanMode(prev => !prev)
    setIsCloneMode(false)
    setIsBobMode(false)
    if (!isPlanMode) {
      setInputValue("")
      if (textareaRef.current) {
        textareaRef.current.focus()
      }
    } else {
      setInputValue("")
    }
  }, [isPlanMode])

  return (
    <div className={`w-full white-box p-5 rounded flex flex-col gap-6 ${containerClassName}`}>
      <AutoExpandTextarea
        ref={textareaRef}
        className="w-full h-fit bg-transparent border-none text-base text-black outline-none"
        placeholder={isCloneMode ? "Clone this website URL: " : isBobMode ? "Let's build a website together!" : strs.inputPlaceholder}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        maxLines={textareaMaxLines}
      />
      <div className="w-full flex items-center justify-between">
        <div className={`flex gap-3 ${leftButtonsClassName}`}>
          <Button className={`flex items-center pl-[0.4rem] gap-2 ${isCloneMode ? 'white-button-shadow' : ''}`} onClick={handleCloneClick}>
            <div className="button-circle white-circle">
              <Image src="/icons/clone.svg" alt={strs.alt.clone} width={50} height={50} className="w-[40%] h-[40%] object-contain" />
            </div>
            {strs.cloneButton}
          </Button>
          <Button className={`flex items-center pl-[0.4rem] gap-2 ${isPlanMode ? 'white-button-shadow' : ''}`} onClick={handlePlanClick}>
            <div className="button-circle white-circle">
              <Image src="/icons/wand.svg" alt={strs.alt.wand} width={50} height={50} className="w-[40%] h-[40%] object-contain" />
            </div>
            {strs.planButton}
          </Button>
        </div>
        <div className={`flex gap-3 ${rightButtonsClassName}`}>
          <Button className="h-8 aspect-square px-0 flex items-center justify-center white-button-shadow">
            <Plus className="h-1/2 w-1/2" strokeWidth={1} />
          </Button>
          <Button className="h-8 aspect-square px-0 flex items-center justify-center" styleClassName="blue-button" onClick={handleSubmit}>
            <AnimationContainer 
              key={isLoading ? "loading" : "arrow"}
              className="w-full h-full flex items-center justify-center relative" 
              animationType="fade"
            >
              {isLoading ? (
                <>
                  <div className="absolute h-5 aspect-square rounded-full border border-white/30 border-t-white animate-spin transition-opacity duration-400 group-hover:opacity-0" />
                  <div className="absolute h-3 aspect-square rounded-full bg-white animate-[circleAnimation_2s_ease-in-out_0.5s_infinite] transition-opacity duration-400 group-hover:opacity-0" />
                  <Square className="absolute z-50 h-3.25 aspect-square fill-white opacity-0 group-hover:opacity-100 transition-opacity duration-400" strokeWidth={0} />
                </>
              ) : (
                <ArrowUp className="h-1/2 w-1/2" strokeWidth={1} />
              )}
            </AnimationContainer>
          </Button>
        </div>
      </div>
    </div>
  )
})