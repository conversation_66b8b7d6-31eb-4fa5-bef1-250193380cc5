"use client"

import { useRef, useEffect, memo, useCallback, forwardRef, useImperativeHandle } from 'react'
import { AutoExpandTextareaProps } from '@/types/components/AutoExpandTextarea'

const AutoExpandTextarea = forwardRef<HTMLTextAreaElement, AutoExpandTextareaProps>(({ 
  placeholder, 
  className = '',
  value,
  onChange,
  onKeyDown,
  maxLines = 4
}, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useImperativeHandle(ref, () => textareaRef.current!)

  const adjustHeight = useCallback(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight)
      const maxHeight = lineHeight * maxLines
      const newHeight = Math.min(textarea.scrollHeight, maxHeight)
      textarea.style.height = `${newHeight}px`
      
      if (textarea.scrollHeight > maxHeight) {
        textarea.style.overflowY = 'auto'
      } else {
        textarea.style.overflowY = 'hidden'
      }
    }
  }, [maxLines])

  useEffect(() => {
    adjustHeight()
  }, [value, adjustHeight])

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    adjustHeight()
    onChange?.(e)
  }, [adjustHeight, onChange])

  return (
    <textarea
      ref={textareaRef}
      className={`resize-none overflow-hidden ${className}`}
      placeholder={placeholder}
      value={value}
      onChange={handleChange}
      onKeyDown={onKeyDown}
      rows={3}
    />
  )
})

AutoExpandTextarea.displayName = 'AutoExpandTextarea'

export default memo(AutoExpandTextarea)