"use client";

import { memo } from "react";
import { AnimatedTextProps } from "@/types/components/AnimatedText";
import { useTextAnimation } from "@/hooks/useTextAnimation";
import ContinuousAnimation from "./text/ContinuousAnimation";
import AnimatedLine from "./text/AnimatedLine";

const AnimatedText = memo(({ text, className = "", onComplete, continuous = false, instant = false }: AnimatedTextProps) => {
    const { lines, colorIndices } = useTextAnimation({ text, onComplete, continuous });
    
    if (continuous) {
        return <ContinuousAnimation text={text} className={className} />;
    }

    let charIndex = 0;

    return (
        <div className={className}>
            {lines.map((line, lineIndex) => {
                const startIndex = charIndex;
                charIndex += line.length + line.split(' ').length - 1;
                
                return (
                    <AnimatedLine
                        key={lineIndex}
                        line={line}
                        lineIndex={lineIndex}
                        startCharIndex={startIndex}
                        colorIndices={colorIndices}
                        instant={instant}
                    />
                );
            })}
        </div>
    );
});

AnimatedText.displayName = "AnimatedText";

export default AnimatedText;