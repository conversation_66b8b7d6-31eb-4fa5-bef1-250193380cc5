"use client";

import { forwardRef } from "react";
import InputSection from "@/components/ui/InputSection";
import ChatMessageComponent from "@/components/ui/ChatMessage";
import type { ChatMessage } from "@/types/chat";
import { 
    shouldAnimate, 
    shouldShowThinking,
    getAnimationCompleteHandler, 
    getTasksCompleteHandler 
} from "@/utils/chat";
import { useMessageAnimationState } from "@/hooks/useMessageAnimationState";

interface BilderViewProps {
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
}

const BilderView = forwardRef<HTMLDivElement, BilderViewProps>(({
    messages,
    isLoading,
    isResponseReady,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted
}, ref) => {
    const { isAnyMessageAnimating } = useMessageAnimationState(messages, isLoading, isResponseReady);
    
    return (
        <>
            <div ref={ref} className="w-full flex-1 min-h-0 overflow-y-auto py-6 pb-20 mask-fade-y">
                {messages.map((message, index) => (
                    <ChatMessageComponent
                        key={message.id}
                        message={message}
                        isAnimated={shouldAnimate(message, index, messages.length, isLoading, isResponseReady)}
                        isThinking={shouldShowThinking(message, index, messages.length, isLoading)}
                        onAnimationComplete={getAnimationCompleteHandler(message, index, messages.length, onAnimationComplete)}
                        onAllTasksCompleted={getTasksCompleteHandler(message, index, messages.length, onAllTasksCompleted)}
                        viewContext="build"
                    />
                ))}
            </div>
            <div className="relative w-full h-fit p-2.5 glass-box">
                <InputSection
                    leftButtonsClassName="hidden"
                    rightButtonsClassName="w-full justify-between"
                    textareaMaxLines={3}
                    onSubmit={onSendMessage}
                    isLoading={isLoading || isAnyMessageAnimating}
                />
            </div>
        </>
    );
});

BilderView.displayName = "BilderView";

export default BilderView;