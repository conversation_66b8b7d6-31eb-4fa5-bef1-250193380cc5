"use client";

import { useRef, forwardRef, useImperativeHandle } from "react";
import SelectorButton from "@/components/ui/SelectorButton";
import BilderView from "./BilderView";
import BrowseView from "./BrowseView";
import ViewWrapper from "./ViewWrapper";
import type { ChatMessage, ViewType } from "@/types/chat";
import { viewOptions } from "@/constants/pages/projects/options";

interface ChatContainerProps {
    activeView: ViewType;
    setActiveView: (view: ViewType) => void;
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
}

export interface ChatContainerRef {
    scrollToBottom: () => void;
}

const ChatContainer = forwardRef<ChatContainerRef, ChatContainerProps>(({
    activeView,
    setActiveView,
    messages,
    isLoading,
    isResponseReady,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted
}, ref) => {
    const chatContainerRef = useRef<HTMLDivElement>(null);

    useImperativeHandle(ref, () => ({
        scrollToBottom: () => {
            if (chatContainerRef.current) {
                chatContainerRef.current.scrollTo({
                    top: chatContainerRef.current.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }
    }));

    return (
        <div className="col-span-3 h-full rounded py-0 flex flex-col gap-0 min-h-0">
            <SelectorButton
                options={viewOptions}
                activeValue={activeView}
                onValueChange={(value: string) => setActiveView(value as ViewType)}
            />
            <div className="h-full w-full flex flex-col gap-0 min-h-0 relative">
                <ViewWrapper viewType="bilder" activeView={activeView}>
                    <BilderView
                        ref={chatContainerRef}
                        messages={messages}
                        isLoading={isLoading}
                        isResponseReady={isResponseReady}
                        onSendMessage={onSendMessage}
                        onAnimationComplete={onAnimationComplete}
                        onAllTasksCompleted={onAllTasksCompleted}
                    />
                </ViewWrapper>
                
                <ViewWrapper viewType="browse" activeView={activeView}>
                    <BrowseView />
                </ViewWrapper>
            </div>
        </div>
    );
});

ChatContainer.displayName = "ChatContainer";

export default ChatContainer;