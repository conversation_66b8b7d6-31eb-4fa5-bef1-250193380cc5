export default function ProjectError({ projectError }: { projectError: string | null }) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="text-red-400 text-6xl mb-4">⚠️</div>
        <h1 className="text-2xl font-bold text-black mb-2">
          {projectError === 'Invalid Project' ? 'Project Not Found' : 'Access Denied'}
        </h1>
        <p className="text-gray-500 mb-6">
          {projectError}
        </p>
        <button
          onClick={() => window.location.href = '/home'}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
        >
          Go Back Home
        </button>
      </div>
    </div>
  )
}