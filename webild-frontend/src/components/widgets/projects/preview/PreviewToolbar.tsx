"use client";

import { memo } from "react";
import IconButton from "@/components/ui/IconButton";
import SelectorButton from "@/components/ui/SelectorButton";
import Image from "next/image";
import { deviceOptions } from "./DeviceOptions";
import { strs } from "@/constants/pages/projects/strs";
import type { DeviceType } from "@/hooks/useDevicePreview";

interface PreviewToolbarProps {
    activeDevice: DeviceType;
    onDeviceChange: (device: DeviceType) => void;
}

const PreviewToolbar = memo(({ activeDevice, onDeviceChange }: PreviewToolbarProps) => {
    return (
        <div className="relative w-full flex justify-between items-center">
            <div className="flex items-center gap-5">
                <IconButton
                    icon="/icons/cursor.svg"
                    alt={strs.alt.browserIcon}
                    label={strs.buttons.edit}
                />
                <div className="flex items-center gap-3">
                    <Image
                        src="/icons/arrow-back.svg"
                        alt={strs.alt.arrowBack}
                        width={50}
                        height={50}
                        className="h-[0.75rem] w-auto object-contain"
                    />
                    <Image
                        src="/icons/arrow-forward.svg"
                        alt={strs.alt.arrowForward}
                        width={50}
                        height={50}
                        className="h-[0.75rem] w-auto object-contain"
                    />
                </div>
            </div>

            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <SelectorButton
                    options={deviceOptions}
                    activeValue={activeDevice}
                    onValueChange={(value: string) => onDeviceChange(value as DeviceType)}
                    wrapperClassName="!bg-transparent-grey !p-0"
                    className="!w-fit !h-8"
                    buttonClassName="!h-8 !w-[2rem] !px-0 aspect-square"
                />
            </div>

            <div className="flex items-center gap-5">
                <Image
                    src="/icons/refresh.svg"
                    alt={strs.alt.refresh}
                    width={50}
                    height={50}
                    className="h-[0.8rem] w-auto object-contain"
                />
                <Image
                    src="/icons/expand.svg"
                    alt={strs.alt.expand}
                    width={50}
                    height={50}
                    className="h-[0.8rem] w-auto object-contain"
                />
                <IconButton
                    icon="/icons/rocket.svg"
                    alt={strs.alt.browserIcon}
                    label={strs.buttons.publish}
                />
            </div>
        </div>
    );
});

PreviewToolbar.displayName = "PreviewToolbar";

export default PreviewToolbar;