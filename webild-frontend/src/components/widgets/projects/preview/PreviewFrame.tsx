"use client";

import { memo, useMemo } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import { strs } from "@/constants/pages/projects/strs";
import LogoAnimation from "@/components/ui/LogoAnimation";
import LoadingTipItem from "./LoadingTipItem";

interface PreviewFrameProps {
    deviceClasses: string;
    activeDevice: string;
    showLoadingState: boolean;
    previewStage: number;
    clonedUrl?: string;
}

const PreviewFrame = memo(({ deviceClasses, activeDevice, showLoadingState, previewStage, clonedUrl }: PreviewFrameProps) => {
    const iframeUrl = useMemo(() => {
        if (showLoadingState) return "";
        if (clonedUrl) return clonedUrl;
        return strs.iframe.urls[previewStage % strs.iframe.urls.length];
    }, [showLoadingState, previewStage, clonedUrl]);

    return (
        <div className="w-full h-full flex items-center justify-center">
            <AnimationContainer
                key={`${activeDevice}-${showLoadingState ? 'loading' : 'iframe'}`}
                className={deviceClasses}
                animationType="fade"
            >
                {showLoadingState ? (
                    <div className="w-full h-full flex items-center justify-center">
                        <div className="relative overflow-hidden p-8 white-button rounded-sm white-button-rounded-sm flex flex-col gap-6">
                            <div className="flex flex-col gap-2 justify-center items-center">
                                <LogoAnimation className="h-5 w-auto cursor-pointer" />
                                <h2 className="text-sm">{strs.loading.title}</h2>
                            </div>
                            <div className="flex flex-col gap-3 justify-center">
                                {strs.loading.tips.map((tip, index) => (
                                    <LoadingTipItem key={index} {...tip} />
                                ))}
                            </div>
                        </div>
                    </div>
                ) : (
                    <iframe
                        key={iframeUrl}
                        src={iframeUrl}
                        className="w-full h-full rounded"
                        title={strs.iframe.title}
                    />
                )}
            </AnimationContainer>
        </div>
    );
});

PreviewFrame.displayName = "PreviewFrame";

export default PreviewFrame;