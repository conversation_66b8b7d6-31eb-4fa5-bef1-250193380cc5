"use client";

import { forwardRef } from "react";
import AnimationContainer from "@/components/layout/AnimationContainer";
import ChatContainer, { ChatContainerRef } from "@/components/widgets/projects/ChatContainer";
import PreviewContainer from "@/components/widgets/projects/PreviewContainer";
import type { ChatMessage, ViewType } from "@/types/chat";

interface BuildViewProps {
    activeView: ViewType;
    setActiveView: (view: ViewType) => void;
    messages: ChatMessage[];
    isLoading: boolean;
    isResponseReady: boolean;
    onSendMessage: (content: string) => void;
    onAnimationComplete: (messageId: string) => void;
    onAllTasksCompleted?: (messageId?: string) => void;
    shouldShowPreview: boolean;
    previewStage: number;
    clonedUrl?: string;
}

const BuildView = forwardRef<ChatContainerRef, BuildViewProps>(({
    activeView,
    setActiveView,
    messages,
    isLoading,
    isResponseReady,
    onSendMessage,
    onAnimationComplete,
    onAllTasksCompleted,
    shouldShowPreview,
    previewStage,
    clonedUrl
}, ref) => {
    return (
        <AnimationContainer
            className="p-6 pt-page-padding h-screen max-h-screen grid grid-cols-10 gap-6"
            animationType="full"
        >
            <ChatContainer
                ref={ref}
                activeView={activeView}
                setActiveView={setActiveView}
                messages={messages}
                isLoading={isLoading}
                isResponseReady={isResponseReady}
                onSendMessage={onSendMessage}
                onAnimationComplete={onAnimationComplete}
                onAllTasksCompleted={onAllTasksCompleted}
            />
            <PreviewContainer showPreview={shouldShowPreview} previewStage={previewStage} clonedUrl={clonedUrl} />
        </AnimationContainer>
    );
});

BuildView.displayName = "BuildView";

export default BuildView;