"use client";

import { ReactNode } from "react";
import type { ViewType } from "@/types/chat";

interface ViewWrapperProps {
    children: ReactNode;
    viewType: ViewType;
    activeView: ViewType;
}

const ViewWrapper = ({ children, viewType, activeView }: ViewWrapperProps) => {
    const isActive = activeView === viewType;
    
    return (
        <div
            className={`absolute inset-0 flex flex-col gap-0 transition-opacity duration-400 will-change-opacity ${
                isActive ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
        >
            {children}
        </div>
    );
};

export default ViewWrapper;