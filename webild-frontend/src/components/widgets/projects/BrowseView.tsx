"use client";

import { memo, useState } from "react";
import BrowseSidebar from "./browse/BrowseSidebar";
import BrowseContent from "./browse/BrowseContent";
import { browseCategories } from "@/data/browseCategories";

const BrowseView = memo(() => {
    const [selectedCategory, setSelectedCategory] = useState<string | null>(browseCategories[0]?.id || null);
    const [selectedSubItem, setSelectedSubItem] = useState<string | null>(null);

    const handleItemSelect = (categoryId: string, subItemId?: string) => {
        setSelectedCategory(categoryId);
        setSelectedSubItem(subItemId || null);
    };

    return (
        <div className="w-full h-full flex mt-5 rounded white-box">
            <div style={{ width: 'calc(50% - var(--vw-1_25))' }}>
                <BrowseSidebar
                    categories={browseCategories}
                    onItemSelect={handleItemSelect}
                />
            </div>
            <div className="flex flex-col gap-4" style={{ width: 'calc(50% + var(--vw-1_25))' }}>
                <BrowseContent
                    selectedCategory={selectedCategory}
                    selectedSubItem={selectedSubItem}
                />
            </div>
        </div>
    );
});

BrowseView.displayName = "BrowseView";

export default BrowseView;