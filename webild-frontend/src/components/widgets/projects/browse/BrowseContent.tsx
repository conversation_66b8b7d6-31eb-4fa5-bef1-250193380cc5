"use client";

import { memo, useCallback } from "react";
import <PERSON><PERSON>ontainer from "@/components/layout/AnimationContainer";
import BrowseSection from "./BrowseSection";
import { BrowseItem } from "@/data/browseContentData";
import { useBrowseContent } from "@/hooks/useBrowseContent";

interface BrowseContentProps {
    selectedCategory: string | null;
    selectedSubItem: string | null;
}

const BrowseContent = memo(({ selectedCategory, selectedSubItem }: BrowseContentProps) => {
    const { sections, contentKey } = useBrowseContent(selectedCategory, selectedSubItem);
    
    const handleItemClick = useCallback((item: BrowseItem) => {
        console.log('Selected item:', item);
    }, []);
    
    return (
        <div className="overflow-y-auto flex flex-col gap-4 p-5 mask-fade-y h-full min-h-0">
            <AnimationContainer animationType="fade" key={contentKey}>
                <div className="flex flex-col gap-8">
                    {sections.map((section) => (
                        <BrowseSection
                            key={section.title}
                            title={section.title}
                            items={section.items}
                            onItemClick={handleItemClick}
                        />
                    ))}
                </div>
            </AnimationContainer>
        </div>
    );
});

BrowseContent.displayName = "BrowseContent";

export default BrowseContent;