"use client";

import type { BrowseCategory } from "@/types/browse";
import { useVaultSidebar } from "@/hooks/useVaultSidebar";
import { getDropdownStyle, getDropdownBorderClass } from "@/utils/browse/sidebar";
import CategoryButton from "./CategoryButton";
import SubItemButton from "./SubItemButton";

interface BrowseSidebarProps {
    categories: BrowseCategory[];
    onItemSelect?: (categoryId: string, subItemId?: string) => void;
}

const BrowseSidebar = ({ categories, onItemSelect }: BrowseSidebarProps) => {
    const {
        expandedCategory,
        selectedCategory,
        selectedSubItem,
        contentRefs,
        mounted,
        handleCategoryClick,
        handleSubItemClick
    } = useVaultSidebar(categories);

    return (
        <ul className="flex flex-col gap-0 p-5 pr-0 list-none">
            {categories.map((category) => {
                const isExpanded = expandedCategory === category.id;
                const isActive = selectedCategory === category.id;

                return (
                    <li key={category.id} className="flex flex-col relative">
                        <div className="relative z-10">
                            <CategoryButton
                                category={category}
                                isActive={isActive}
                                isExpanded={isExpanded}
                                onClick={() => handleCategoryClick(category, onItemSelect)}
                            />
                        </div>

                        <div
                            ref={(el) => { contentRefs.current[category.id] = el; }}
                            style={getDropdownStyle(isExpanded, mounted, contentRefs.current[category.id]?.scrollHeight)}
                            className={getDropdownBorderClass(isExpanded)}
                        >
                            <div className="flex flex-col relative transform origin-top">
                                <div className="flex flex-col gap-0.5 py-[0.25rem] px-5 relative">
                                    {category.subItems?.map((subItem) => {
                                        const isSubActive = selectedSubItem === subItem.id;
                                        return (
                                            <SubItemButton
                                                key={subItem.id}
                                                subItem={subItem}
                                                isActive={isSubActive}
                                                onClick={() => handleSubItemClick(category.id, subItem.id, onItemSelect)}
                                            />
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    </li>
                );
            })}
        </ul>
    );
};

export default BrowseSidebar;