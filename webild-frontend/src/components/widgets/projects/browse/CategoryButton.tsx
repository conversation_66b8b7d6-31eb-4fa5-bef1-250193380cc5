"use client";

import { ChevronDown } from "lucide-react";
import type { BrowseCategory } from "@/types/browse";
import { getCategoryButtonClass, getChevronClass } from "@/utils/browse/sidebar";

interface CategoryButtonProps {
    category: BrowseCategory;
    isActive: boolean;
    isExpanded: boolean;
    onClick: () => void;
}

const CategoryButton = ({ category, isActive, isExpanded, onClick }: CategoryButtonProps) => {
    return (
        <button
            onClick={onClick}
            className={getCategoryButtonClass(isActive)}
        >
            <div className="z-10 flex justify-between items-center px-5 relative">
                <div className="flex items-center gap-3 flex-grow relative overflow-hidden">
                    <div className="h-[var(--text-sm)] w-auto flex-shrink-0">
                        {category.icon}
                    </div>
                    <span className="text-sm">
                        {category.label}
                    </span>
                </div>
                <ChevronDown className={getChevronClass(isExpanded)} />
            </div>
            <div className={`absolute inset-0 transition-opacity duration-[400ms] ${isActive ? 'opacity-100' : 'opacity-0'}`}>
                <div className="w-full h-full white-box" />
            </div>
        </button>
    );
};

export default CategoryButton;