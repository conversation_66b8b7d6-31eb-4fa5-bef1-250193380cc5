"use client";

import { memo, useState } from "react";
import Image from "next/image";
import type { BrowseItem } from "@/data/browseContentData";
import { ArrowUpRight } from "lucide-react";

interface BrowseItemCardProps {
    item: BrowseItem;
    onClick?: (item: BrowseItem) => void;
}

const BrowseItemCard = memo(({ item, onClick }: BrowseItemCardProps) => {
    const [isHovered, setIsHovered] = useState(false);
    
    return (
        <div 
            className="relative flex flex-col gap-1 p-2 rounded-sm white-button white-button-thin white-button-rounded-sm cursor-pointer"
            onClick={() => onClick?.(item)}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div className="w-full h-30 rounded-sm overflow-hidden relative">
                <div className={`absolute top-2 right-2 z-30 transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
                    <button className="relative cursor-pointer h-5 aspect-square rounded-full white-button flex items-center justify-center">
                        <ArrowUpRight className="w-1/2 h-1/2" />
                    </button>
                </div>
                <Image
                    src={item.preview}
                    alt={item.title}
                    width={200}
                    height={200}
                    className="w-full h-full object-cover"
                />
                <video
                    src={item.video}
                    autoPlay
                    loop
                    muted
                    playsInline
                    className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
                />
            </div>
            <p className="text-xs">{item.title}</p>
        </div>
    );
});

BrowseItemCard.displayName = "BrowseItemCard";

export default BrowseItemCard;