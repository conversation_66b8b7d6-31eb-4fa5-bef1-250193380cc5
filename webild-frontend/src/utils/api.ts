import { CreateProjectRequest, CreateProjectResponse, GetProjectIDResponse } from '@/types/project';

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: unknown
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Makes an authenticated API request
 */
async function makeAuthenticatedRequest<T>(
  endpoint: string,
  options: RequestInit,
  token: string
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new ApiError(
      `API error! Status: ${response.status}`,
      response.status,
      errorData
    );
  }

  return response.json();
}

/**
 * Creates a new project via the backend API
 */
export async function createProjectAPI(
  data: CreateProjectRequest,
  token: string
): Promise<CreateProjectResponse> {
  return makeAuthenticatedRequest<CreateProjectResponse>(
    '/projects',
    {
      method: 'POST',
      body: JSON.stringify(data),
    },
    token
  );
}

export async function getProjectIDAPI(
  projectId: string,
  token: string
): Promise<GetProjectIDResponse> {
  return makeAuthenticatedRequest<GetProjectIDResponse>(
    `/projects/${projectId}`,
    {
      method: 'GET',
    },
    token
  );
}