import type { AIResponse } from "@/types/aiResponse";
import { aiResponses } from "@/data/aiResponses";
import { getCloneAiResponses } from "@/data/cloneAiResponses";
import { extractUrlFromClonePrompt } from "@/utils/cloneUrl";

export const getAiResponses = (
    projectData: { mode: 'clone' | 'build' | 'bob'; prompt?: string } | null | undefined
): AIResponse[] | undefined => {
    if (!projectData) return undefined;
    
    if (projectData.mode === 'clone') {
        const url = projectData.prompt ? extractUrlFromClonePrompt(projectData.prompt) : null;
        return getCloneAiResponses(url || undefined);
    }
    
    return aiResponses;
};