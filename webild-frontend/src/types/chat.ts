export type MessageType = "text" | "tasks" | "iframe";

export interface TaskItem {
  text: string;
  completed?: boolean;
}

export interface TasksContent {
  title: string;
  tasks: TaskItem[];
}

export interface IframeContent {
  url: string;
  title?: string;
  completionTime?: number;
}

export interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string | TasksContent | IframeContent;
  type?: MessageType;
  showPreview?: boolean;
  taskCompletionTimes?: number[];
  iframeCompleted?: boolean;
  timestamp: Date;
  animationComplete?: boolean;
  animationStartTime?: number;
  manuallyStopped?: boolean;
}

export interface ChatMessageProps {
  message: ChatMessage;
  isAnimated?: boolean;
}

export type ViewType = "bilder" | "browse";