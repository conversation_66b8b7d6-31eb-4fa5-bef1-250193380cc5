export interface ProjectData {
  id: string;
  mode: "build" | "clone" | "bob";
  prompt: string;
  createdAt: string;
}

export interface CreateProjectRequest {
  name: string;
}

export interface ProjectVersion {
  id: string;
  project_id: string;
  version_number: number;
  version_prompt: string;
  version_branch: string;
  version_status: string;
  feedback_info: string | null;
  feedback_score: number | null;
  createdAt: string;
  updatedAt: string;
}

export interface BaseProject {
  id: string;
  name: string;
  repositoryUrl: string;
  repositoryOwner: string;
  repositoryName: string;
  activeVersionId: string;
  publish_url: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface BackendProject extends BaseProject {
  activeVersion: ProjectVersion;
}

export interface ProjectWithVersions extends BaseProject {
  versions: ProjectVersion[];
}

export interface CreateProjectResponse {
  code: number;
  data: BackendProject;
}

export interface GetProjectIDResponse {
  code: number;
  data: ProjectWithVersions;
}
