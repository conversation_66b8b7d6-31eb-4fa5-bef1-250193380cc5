{"name": "backend", "version": "0.0.1", "engines": {"node": "24.4.1"}, "private": true, "scripts": {"preinstall": "node ./scripts/ensure-npm.js", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky"}, "lint-staged": {"**/*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@anthropic-ai/sdk": "0.57.0", "@bull-board/api": "6.12.0", "@bull-board/express": "6.12.0", "@bull-board/nestjs": "6.12.0", "@clerk/backend": "2.5.1", "@nestjs/bullmq": "11.0.3", "@nestjs/common": "11.0.1", "@nestjs/config": "4.0.2", "@nestjs/core": "11.0.1", "@nestjs/platform-express": "11.0.1", "@nestjs/sequelize": "11.0.0", "@nestjs/swagger": "11.2.0", "@octokit/rest": "22.0.0", "adm-zip": "0.5.16", "bullmq": "5.56.9", "express-basic-auth": "1.2.1", "ioredis": "5.7.0", "mysql2": "3.14.2", "reflect-metadata": "0.2.2", "rxjs": "7.8.1", "sequelize": "6.37.7", "sequelize-typescript": "2.1.6", "unzipper": "0.12.3"}, "devDependencies": {"@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@nestjs/cli": "11.0.0", "@nestjs/schematics": "11.0.0", "@nestjs/testing": "11.0.1", "@swc/cli": "0.6.0", "@swc/core": "1.10.7", "@types/adm-zip": "0.5.7", "@types/express": "5.0.0", "@types/jest": "29.5.14", "@types/multer": "2.0.0", "@types/node": "22.10.7", "@types/sequelize": "4.28.20", "@types/supertest": "6.0.2", "@types/unzipper": "0.10.11", "eslint": "9.18.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-import": "2.32.0", "eslint-plugin-prettier": "5.2.2", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "16.1.2", "prettier": "3.4.2", "sequelize-cli": "6.6.3", "source-map-support": "0.5.21", "supertest": "7.0.0", "ts-jest": "29.2.5", "ts-loader": "9.5.2", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.7.3", "typescript-eslint": "8.20.0"}}