NODE_ENV=development
PROJECT_NAME=webild-backend
API_PORT=3000

MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=app
MYSQL_ROOT_PASSWORD=root
MYSQL_USER=user
MYSQL_PASSWORD=user

REDIS_HOST=localhost
REDIS_PORT=6379

CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
CLERK_USER_JWT_FOR_SWAGGER=

ANTHROPIC_API_KEY=
ANTHROPIC_API_MODEL=claude-3-5-sonnet-20241022
ANTHROPIC_API_MAX_TOKENS=1024

GITHUB_TOKEN=

WHISPER_ENDPOINT=http://localhost:8000/whisper/transcribe

BULL_BOARD_PASSWORD=admin
