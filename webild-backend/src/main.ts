import * as process from 'node:process';

import { NestFactory } from '@nestjs/core';

import { AppModule } from './app.module';
import { setupSwagger } from './common/utils/swagger-setup';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors();

  if (process.env.NODE_ENV !== 'production') {
    setupSwagger(app);
  }

  await app.listen(process.env.API_PORT ?? 3000);
}
void bootstrap();
