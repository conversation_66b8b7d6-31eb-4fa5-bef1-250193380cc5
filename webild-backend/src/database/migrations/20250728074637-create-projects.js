'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('projects', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.literal('(UUID())'),
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      repository_url: {
        type: Sequelize.STRING,
      },
      active_version_id: {
        type: Sequelize.UUID,
        allowNull: true,
        defaultValue: null,
      },
      publish_url: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      repository_owner: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      repository_name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('projects');
  },
};
