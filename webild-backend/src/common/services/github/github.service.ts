import { Injectable } from '@nestjs/common';
import { Octokit } from '@octokit/rest';

import {
  bufferFromDownloadArtifact,
  extractLogFromZip,
} from '../../../common/utils/extract-logs-from-zip';

@Injectable()
export class GithubService {
  private readonly octokit = new Octokit({
    auth: process.env.GITHUB_TOKEN,
  });

  async createRepository(name: string, isPrivate = false) {
    try {
      const response = await this.octokit.repos.createForAuthenticatedUser({
        name,
        private: isPrivate,
        auto_init: true,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to create repository:', error);
      throw new Error('Failed to create repository');
    }
  }

  async createBranch(owner: string, repo: string, newBranch: string, baseBranch = 'main') {
    try {
      const { data: refData } = await this.octokit.git.getRef({
        owner,
        repo,
        ref: `heads/${baseBranch}`,
      });

      const baseSha = refData.object.sha;

      const { data: newRef } = await this.octokit.git.createRef({
        owner,
        repo,
        ref: `refs/heads/${newBranch}`,
        sha: baseSha,
      });

      return newRef;
    } catch (error) {
      console.error('Failed to create branch:', error);
      throw new Error('Failed to create branch');
    }
  }

  async deployToBranch(
    owner: string,
    repo: string,
    branch: string,
    filePath: string,
    content: string,
    commitMessage: string,
  ): Promise<any> {
    try {
      const encodedContent = Buffer.from(content).toString('base64');

      let sha: string | undefined;
      try {
        const { data } = await this.octokit.repos.getContent({
          owner,
          repo,
          path: filePath,
          ref: branch,
        });

        if (!Array.isArray(data)) {
          sha = data.sha;
        }
      } catch (error) {
        console.error('Failed to get file contents:', error);
      }

      const response = await this.octokit.repos.createOrUpdateFileContents({
        owner,
        repo,
        path: filePath,
        message: commitMessage,
        content: encodedContent,
        branch,
        sha,
      });

      return response.data;
    } catch (error) {
      console.error('Failed to deploy to branch:', error);
      throw new Error('Failed to deploy to branch');
    }
  }

  async triggerWorkflowDispatch(
    owner: string,
    repo: string,
    branch: string,
    workflowFileName: string = 'build.yml',
  ) {
    try {
      await this.octokit.rest.actions.createWorkflowDispatch({
        owner,
        repo,
        workflow_id: workflowFileName,
        ref: branch,
        inputs: {
          branch: branch,
        },
      });
    } catch (error) {
      console.error('Error triggering workflow:', error);
    }
  }

  async waitForWorkflowCompletion(owner: string, repo: string, branch: string) {
    let runId: number | undefined;

    while (!runId) {
      const runs = await this.octokit.rest.actions.listWorkflowRunsForRepo({
        owner,
        repo,
        branch,
      });

      runId = runs.data.workflow_runs.find((run) => run.status !== 'completed')?.id;
      if (!runId) {
        await new Promise((resolve) => setTimeout(resolve, 5000));
        continue;
      }

      console.log(`� Workflow run detected! Run ID: ${runId}`);
    }

    console.log(`⏳ Waiting for run ${runId} to complete...`);

    while (true) {
      const run = await this.octokit.rest.actions.getWorkflowRun({
        owner,
        repo,
        run_id: runId,
      });

      const { status, conclusion } = run.data;
      console.log(`Status: ${status}, Conclusion: ${conclusion || 'N/A'}`);

      if (status === 'completed') {
        if (conclusion === 'success') {
          console.log('✅ Build succeeded!');
          return { success: true };
        } else {
          console.error(`❌ Build failed with conclusion: ${conclusion}`);
          const artifacts = await this.octokit.rest.actions.listWorkflowRunArtifacts({
            owner,
            repo,
            run_id: runId,
          });

          const logArtifact = artifacts.data.artifacts.find((a) => a.name === 'build-log');

          if (!logArtifact) {
            return { success: false, error: 'Build failed but no log artifact found' };
          }

          const dl = await this.octokit.rest.actions.downloadArtifact({
            owner,
            repo,
            artifact_id: logArtifact.id,
            archive_format: 'zip',
          });

          const zipBuffer = await bufferFromDownloadArtifact(dl);
          let logContent = 'No log content found';
          try {
            logContent = await extractLogFromZip(zipBuffer);
          } catch (error) {
            console.error('Error extracting log from zip:', error);
            logContent = 'Error extracting log file';
          }

          return { success: false, error: logContent };
        }
      }

      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  }

  async forcePushBranchToMain(
    owner: string,
    repo: string,
    sourceBranch: string,
    targetBranch = 'main',
  ) {
    try {
      const { data: sourceBranchRef } = await this.octokit.rest.git.getRef({
        owner,
        repo,
        ref: `heads/${sourceBranch}`,
      });
      const sourceCommitSha = sourceBranchRef.object.sha;

      await this.octokit.rest.git.updateRef({
        owner,
        repo,
        ref: `heads/${targetBranch}`,
        sha: sourceCommitSha,
        force: true,
      });

      console.log(`Successfully force pushed '${sourceBranch}' to '${targetBranch}'.`);
    } catch (error) {
      console.error('Error force pushing:', error);
    }
  }

  async ensureWorkflowIsIndexed(
    owner: string,
    repo: string,
    workflowFileName: string = 'build.yml',
  ) {
    let found = false;
    for (let attempt = 1; attempt <= 10; attempt++) {
      const workflows = await this.octokit.rest.actions.listRepoWorkflows({ owner, repo });
      found = workflows.data.workflows.some((wf) => wf.path.endsWith(workflowFileName));

      if (found) {
        console.log(`✅ Workflow "${workflowFileName}" is now indexed by GitHub.`);
        return;
      }

      console.log(`⏳ Workflow not found yet, retrying... (${attempt}/10)`);
      await new Promise((resolve) => setTimeout(resolve, 5000)); // wait 5s
    }

    throw new Error(`Workflow "${workflowFileName}" not found after waiting.`);
  }
}
