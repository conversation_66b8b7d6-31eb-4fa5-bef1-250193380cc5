export const fixCodeErrorsPrompt = `
Please fix the build errors in the project you previously generated and that is now in my GitHub repository.

OUTPUT ONLY THE FILENAMES THAT NEED CHANGES.

OUTPUT RULES (CRITICAL)
- Return a plain text list of file paths ONLY.
- One path per line.
- Include only files that must be ADDED or UPDATED to fix the build.
- Do not include files to delete.
- No code, no explanations, no headers, no bullets, no markdown, no backticks, no blank lines.
- Use correct relative paths from the repository root.
- Do not truncate the list.

After I receive this list, I will ask for the content of each file individually using the exact file path.
 `;
