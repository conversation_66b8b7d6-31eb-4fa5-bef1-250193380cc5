export const generateCodePrompt = `
You're assisting in building a full, production-ready **Next.js 15 project using TypeScript**.
Please follow these instructions carefully and precisely.

## PROJECT GOAL

We are generating a complete, functional, and optimized **Next.js 15 project** (using the \`app/\` directory, TypeScript, Tailwind CSS, and proper SEO setup). This project will be deployed as-is, so it must be complete and working.

---

## BASE STRUCTURE

Use the base format of the following GitHub repository:  
**https://github.com/vitaliimulika-dev/next**

- Use its directory structure, configuration style, and **package.json** as the baseline for this project.
- You may **extend or modify the base project** only where necessary — e.g., to support additional components, packages, or functionality as outlined below.
- Simplify project structure as much as possible, but do not remove any required files or directories.
- Ensure the project is built with **Next.js 15** specifically.

---

## RULES FOR COMPONENTS & UI

- IMPORTANT: Use **@radix-ui** for reusable components.
- Use **Tailwind CSS** for all styling.
- All styling **must be verified to work correctly** (Tail unfortunate classes, base styles, custom styles, etc).
- IMPORTANT: Add \`"use client"\` at the top of **every single component file** (e.g., in \`components/\` or \`ui/\`), regardless of whether it uses hooks or not.
- IMPORTANT: Use the **\`postcss.config.mjs\` from the GitHub project** as the base, and do not override or omit any necessary config.
- All custom or third-party UI components must be fully **copied locally** into the codebase (e.g., inside \`components/\`, \`ui/\`, etc.).
- Do **not** rely on external UI libraries unless explicitly allowed.
- All code must be included directly — no external references.
- If no specific image is provided, use a default placeholder image.

---

## STEP 1 — FILE STRUCTURE ONLY

DO NOT generate any file content yet.

At this stage, I only want a **complete file structure** for the project.

### Format:
Return a plain text list of **all files** required in the project, with each file path on a new line.

Example format:
app/layout.tsx
app/page.tsx
components/ui/button.tsx
components/ui/card.tsx
styles/globals.css
public/images/logo.svg
next.config.js
tailwind.config.ts
tsconfig.json
package.json
---

## IMPORTANT — PACKAGE.JSON

- Use the **\`package.json\` from the GitHub project** as a base:  
  https://github.com/vitaliimulika-dev/next/blob/main/package.json
  
- Ensure the project uses **Next.js 15** explicitly in the \`package.json\`.
- Add or extend it **only if necessary**, for example:
  - ✅ For each file, check all imports and ensure required packages are added to \`package.json\`
  - ✅ Include packages for tools like ESLint, Prettier, or Radix UI
  - ✅ Ensure all packages needed by the used configuration files are included

---

## STRUCTURE OUTPUT RULES

- Include **every file** that will exist in the final project.
- File extensions must be correct: \`.tsx\`, \`.ts\`, \`.js\`, \`.json\`, \`.css\`, etc.
- Do **not** list empty folders — only include paths to actual files.
- Do **not** include comments, markdown formatting, or explanation — just a clean file list.
- Do **not** truncate the file list.
- Do **not** ask whether to continue — return the entire file list in one response.
- Ensure all imported components exist in the structure you provide.

---

## FINAL REMINDERS

- IMPORTANT: **"use client"** must be added at the top of **every** component file (e.g., in \`components/\` or \`ui/\` directories).
- IMPORTANT: Use the correct and working **\`postcss.config.mjs\`** from the provided base project.
- IMPORTANT️: Double-check that **Tailwind and all styles work fully and correctly** across the app.
- IMPORTANT: The project must use **Next.js 15** explicitly in \`package.json\`.
- IMPORTANT: Use **@radix-ui** for reusable components
- IMPORTANT: Double check imports paths.

Once I receive the structure, I will ask for the content of each file **individually** using the exact file path.
`;
