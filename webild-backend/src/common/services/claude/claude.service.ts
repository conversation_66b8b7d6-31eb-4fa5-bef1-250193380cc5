import { Anthropic } from '@anthropic-ai/sdk';
import { Injectable } from '@nestjs/common';

import { fixCodeErrorsPrompt } from './prompts/fixCodeErrorsPrompt';
import { generateCodePrompt } from './prompts/generateCodePrompt';
import { getFileContentPrompt } from './prompts/getFileContentPrompt';
import { optimizeUserPrompt } from './prompts/optimizeUserPrompt';
import { welcomePrompt } from './prompts/welcomePrompt';
import { Message } from '../../../modules/messages/message.entity';

export enum SystemPrompts {
  OPTIMIZE = 'optimize',
  WELCOME = 'welcome',
  GENERATE_CODE = 'generate_code',
  GET_FILE_CONTENT = 'get_file_content',
  FIX_CODE_ERRORS = 'fix_code_errors',
}

const SYSTEM_PROMPT_MAP: Record<SystemPrompts, string> = {
  [SystemPrompts.OPTIMIZE]: optimizeUserPrompt,
  [SystemPrompts.WELCOME]: welcomePrompt,
  [SystemPrompts.GENERATE_CODE]: generateCodePrompt,
  [SystemPrompts.GET_FILE_CONTENT]: getFileContentPrompt,
  [SystemPrompts.FIX_CODE_ERRORS]: fixCodeErrorsPrompt,
};

@Injectable()
export class ClaudeService {
  private readonly anthropic = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  });

  private readonly model = process.env.ANTHROPIC_API_MODEL || 'claude-3-5-sonnet-20241022';
  private readonly maxTokens = parseInt(process.env.ANTHROPIC_API_MAX_TOKENS || '1024', 10);

  async sendMessage(
    prompt: string,
    systemPrompts: SystemPrompts[] = [],
    history: Message[] = [],
  ): Promise<string> {
    try {
      const systemMessages = systemPrompts.map((key) => ({
        role: 'user' as const,
        content: SYSTEM_PROMPT_MAP[key],
      }));

      const userMessage = {
        role: 'user' as const,
        content: `User prompt: ${prompt}`,
      };

      const historyMessages = history.map((item) => ({
        role: item.type,
        content: item.message,
      }));

      const message = await this.anthropic.messages.create({
        model: this.model,
        max_tokens: this.maxTokens,
        messages: [...historyMessages, ...systemMessages, userMessage],
      });

      return message.content
        .filter((part) => part.type === 'text')
        .map((part) => part.text)
        .join('');
    } catch (error) {
      console.error('Claude SDK error:', error);
      throw new Error('Failed to communicate with Claude API');
    }
  }
}
