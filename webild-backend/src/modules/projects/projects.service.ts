import * as fs from 'fs';
import * as path from 'path';

import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

import { CreateProjectVersionDto, UpdateProjectVersionDto } from './dto/create-project-version.dto';
import { CreateProjectDto } from './dto/create-project.dto';
import { GetProjectDto } from './dto/get-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ProjectUser } from './project-user.entity';
import { ProjectVersion } from './project-version.entity';
import { BuildFileContent, ProjectUserRole, ProjectVersionStatus } from './project.constants';
import { Project } from './project.entity';
import { SystemPrompts } from '../../common/services/claude/claude.service';
import { GithubService } from '../../common/services/github/github.service';
import { paginateCursor } from '../../common/utils/paginate-cursor.util';
import { MessageTypes } from '../messages/message.constants';
import { MessagesService } from '../messages/messages.service';
import { QueueService } from '../queue/queue.service';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectModel(Project) private projectModel: typeof Project,
    @InjectModel(ProjectVersion) private versionModel: typeof ProjectVersion,
    @InjectModel(ProjectUser) private projectUserModel: typeof ProjectUser,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    private readonly gitHubService: GithubService,
    private readonly queueService: QueueService,
  ) {}

  private readonly tempProjPath = './tmp/';

  async createProject(dto: CreateProjectDto, userId: string) {
    const repositoryUrl = 'https://github.com';

    const project = await this.projectModel.create({ ...dto, repositoryUrl }, { returning: true });

    await this.projectUserModel.create({
      user_id: userId,
      project_id: project.id,
      role: ProjectUserRole.Owner,
    });

    const repository = await this.gitHubService.createRepository(project.id);
    project.repositoryUrl = repository.html_url;
    project.repositoryOwner = repository.owner.login;
    project.repositoryName = repository.name;
    await project.save();

    const version = await this.createProjectVersion({ version_prompt: '', project_id: project.id });

    await this.messagesService.initAIDialogue(userId, project.id, version.id);

    const createdProject = await this.projectModel.findByPk(project.id, {
      include: ['activeVersion'],
    });

    if (!createdProject) {
      throw new Error('Failed to create project');
    }

    return createdProject.get({ plain: true });
  }

  async createProjectVersion(dto: CreateProjectVersionDto & { project_id: string }) {
    const project = await this.projectModel.findByPk(dto.project_id);
    if (!project) throw new Error('Project not found');

    const lastVersion = await this.versionModel.findOne({
      where: { project_id: dto.project_id },
      order: [['createdAt', 'DESC']],
    });

    const versionNumber = lastVersion ? lastVersion.version_number + 1 : 1;
    const versionBranch = `version_${versionNumber}`;

    await this.gitHubService.createBranch(
      project.repositoryOwner,
      project.repositoryName,
      versionBranch,
    );

    const version = await this.versionModel.create(
      {
        ...dto,
        version_number: versionNumber,
        version_branch: versionBranch,
      },
      { returning: true },
    );

    await this.projectModel.update(
      { activeVersionId: version.id },
      { where: { id: dto.project_id } },
    );

    return version.get({ plain: true });
  }

  async getProject(dto: GetProjectDto) {
    const project = await this.projectModel.findByPk(dto.id, {
      include: ['versions'],
    });

    if (!project) {
      throw new Error('Project not found');
    }

    return project.get({ plain: true });
  }

  async getProjectVersion(projectId: string, projectVersionId: string) {
    const version = await this.versionModel.findOne({
      where: {
        id: projectVersionId,
        project_id: projectId,
      },
    });

    if (!version) throw new Error('Project version not found');
    return version;
  }

  async getUserProjects(params: { userId: string; limit: number; cursor: string | undefined }) {
    const { userId, limit, cursor } = params;

    return await paginateCursor(Project, {
      limit,
      cursor,
      include: [
        {
          model: ProjectUser,
          where: { user_id: userId },
          attributes: [],
        },
      ],
      orderBy: 'created_at',
      orderDirection: 'ASC',
    });
  }

  async updateProject(id: string, dto: Partial<UpdateProjectDto>) {
    const project = await this.projectModel.findByPk(id);
    if (!project) {
      throw new Error('Project not found');
    }

    if (dto.activeVersionId) {
      const version = await this.versionModel.findOne({
        where: { id: dto.activeVersionId, project_id: id },
      });

      if (!version) {
        throw new Error('activeVersionId is invalid or does not belong to the project');
      }
    }

    await project.update(dto);
    return project.get({ plain: true });
  }

  async updateProjectVersion(projectId: string, versionId: string, dto: UpdateProjectVersionDto) {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });

    if (!version) throw new Error('Project version not found');

    await version.update(dto);
    return version.get({ plain: true });
  }

  async executeVersionPrompt(projectId: string, versionId: string, userId: string) {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });
    if (!version) throw new Error('Version not found');

    const combinedPrompt = await this.messagesService.generateVersionPrompt(projectId, versionId);
    version.version_prompt = combinedPrompt;
    version.version_status = ProjectVersionStatus.Processing;
    await version.save();

    try {
      await this.queueService.enqueueProjectGenerationJob(
        combinedPrompt,
        userId,
        projectId,
        versionId,
      );
    } catch (error) {
      version.version_status = ProjectVersionStatus.Drafted;
      await version.save();
      console.error('Failed to create project files', error);
      throw new Error('Failed to create project files');
    }

    return version.get({ plain: true });
  }

  createTempProjectFolder(path: string) {
    const fullPath = `${this.tempProjPath}${path}`;
    if (fs.existsSync(fullPath)) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    }
    fs.mkdirSync(fullPath, { recursive: true });

    return fullPath;
  }

  writeParsedFile(filePath: string, content: string, root = './tmp/claude-project') {
    const fullPath = path.join(root, filePath);
    const dir = path.dirname(fullPath);

    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(fullPath, content, 'utf-8');
  }

  async getProjectStructure(
    userId: string,
    projectId: string,
    versionId: string,
    prompt: string,
    systemPrompts: SystemPrompts[],
  ) {
    const generatedMessage = await this.messagesService.askNStoreAI(
      userId,
      projectId,
      versionId,
      {
        message: `Generate code based on the following requirements:\n\n${prompt}`,
        type: MessageTypes.User,
      },
      systemPrompts,
    );

    return generatedMessage.message
      .split('\n')
      .map((line) => line.trim())
      .filter((path) => path.length > 0 && path !== '```');
  }

  async createProjectFromFiles(
    filesPaths: string[],
    userId: string,
    projectId: string,
    versionId: string,
    initialRun: boolean = true,
  ) {
    const projectPath = this.createTempProjectFolder(projectId);

    for (let i = 0; i < filesPaths.length; i++) {
      const response = await this.messagesService.askNStoreAI(
        userId,
        projectId,
        versionId,
        {
          message: `File name ${filesPaths[i]}`,
          type: MessageTypes.User,
        },
        [SystemPrompts.GET_FILE_CONTENT],
        false,
      );

      let fileContent = response.message;
      let filePath = filesPaths[i];

      if (filePath === 'package.json') {
        try {
          const parsedJson: { dependencies: Record<string, string> } = JSON.parse(fileContent);

          if (parsedJson.dependencies && typeof parsedJson.dependencies['next'] === 'string') {
            parsedJson.dependencies['next'] = '15.4.5';
          }

          fileContent = JSON.stringify(parsedJson, null, 2);
        } catch (error) {
          console.error('Failed to parse or update package.json', error);
        }
      }

      if (
        filePath.startsWith('components/') &&
        filePath.endsWith('.tsx') &&
        !fileContent.startsWith('"use client"')
      ) {
        fileContent = `"use client";\n\n${fileContent}`;
      }

      if (filePath === 'postcss.config.mjs' && initialRun) {
        fileContent =
          'export default {\n' +
          '    plugins: {\n' +
          '        tailwindcss: {},\n' +
          '        autoprefixer: {},\n' +
          '    },\n' +
          '};';
      }

      if (filePath === 'next.config.js' && initialRun) {
        fileContent =
          'import type { NextConfig } from "next";\n' +
          '\n' +
          'const nextConfig: NextConfig = {\n' +
          '  /* config options here */\n' +
          '};\n' +
          '\n' +
          'export default nextConfig;';
        filePath = 'next.config.ts';
      }

      this.writeParsedFile(filePath, fileContent, projectPath);
    }
    if (initialRun) {
      this.writeParsedFile('.github/workflows/build.yml', BuildFileContent, projectPath);
    }
  }

  async pushProjectToGitHub(projectId: string, versionId: string) {
    const project = await this.projectModel.findByPk(projectId);
    if (!project) throw new Error('Project not found');
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });
    if (!version) throw new Error('Project version not found');

    const { repositoryOwner, repositoryName, id } = project;
    const branch = version.version_branch;
    const projectPath = `${this.tempProjPath}${id}`;

    const uploadFile = async (filePath: string) => {
      const relativePath = path.relative(projectPath, filePath).replace(/\\/g, '/');
      const content = fs.readFileSync(filePath, 'utf8');
      await this.gitHubService.deployToBranch(
        repositoryOwner,
        repositoryName,
        branch,
        relativePath,
        content,
        `Add ${relativePath}`,
      );
    };

    const walk = async (dir: string): Promise<void> => {
      const entries = fs.readdirSync(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        if (entry.isDirectory()) {
          await walk(fullPath);
        } else {
          await uploadFile(fullPath);
        }
      }
    };

    await walk(projectPath);

    fs.rmSync(projectPath, { recursive: true, force: true });
    return {
      repositoryOwner,
      repositoryName,
      branch,
    };
  }
}
