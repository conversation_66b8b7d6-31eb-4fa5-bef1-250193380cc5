import {
  Table,
  Column,
  <PERSON>,
  <PERSON>Type,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ult,
  <PERSON><PERSON>ey,
  BelongsTo,
} from 'sequelize-typescript';

import { ProjectVersionStatus } from './project.constants';
import { Project } from './project.entity';

@Table({ tableName: 'project_versions', timestamps: true })
export class ProjectVersion extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  declare id: string;

  @ForeignKey(() => Project)
  @Column({ field: 'project_id', allowNull: false })
  declare project_id: string;

  @Column({ allowNull: false })
  declare version_number: number;

  @Column({ allowNull: true, type: DataType.TEXT })
  declare version_prompt: string;

  @Column({ allowNull: false })
  declare version_branch: string;

  @Column({ allowNull: false, defaultValue: ProjectVersionStatus.Drafted })
  declare version_status: string;

  @Column({ type: DataType.TEXT })
  declare feedback_info?: string;

  @Column
  declare feedback_score?: number;

  @Column({ field: 'created_at' })
  declare readonly createdAt: Date;

  @Column({ field: 'updated_at' })
  declare readonly updatedAt: Date;

  @BelongsTo(() => Project)
  declare project?: Project;
}
