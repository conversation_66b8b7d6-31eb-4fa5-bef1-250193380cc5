export enum ProjectVersionStatus {
  Published = 'published',
  Drafted = 'drafted',
  Executed = 'executed',
  Processing = 'processing',
}

export enum ProjectUserRole {
  Owner = 'owner',
}

export const BuildFileContent =
  'name: Build Check\n' +
  '\n' +
  'on:\n' +
  '  workflow_dispatch:\n' +
  '    inputs:\n' +
  '      branch:\n' +
  "        description: 'Branch to run the workflow on'\n" +
  '        required: true\n' +
  "        default: 'main'\n" +
  '\n' +
  'jobs:\n' +
  '  build:\n' +
  '    runs-on: ubuntu-latest\n' +
  '    steps:\n' +
  '      - name: Checkout branch\n' +
  '        uses: actions/checkout@v4\n' +
  '        with:\n' +
  '          ref: ${{ github.event.inputs.branch }}\n' +
  '\n' +
  '      - name: Set up Node.js\n' +
  '        uses: actions/setup-node@v4\n' +
  '        with:\n' +
  "          node-version: '24'\n" +
  '\n' +
  '      - name: Install dependencies\n' +
  '        run: npm install\n' +
  '\n' +
  '      - name: Build project\n' +
  '        run: |\n' +
  '          set -e\n' +
  '          set -o pipefail\n' +
  '          npm run build 2>&1 | tee build.log\n' +
  '\n' +
  '      - name: Upload build log on failure\n' +
  '        if: failure()\n' +
  '        uses: actions/upload-artifact@v4\n' +
  '        with:\n' +
  '          name: build-log\n' +
  '          path: build.log\n';
