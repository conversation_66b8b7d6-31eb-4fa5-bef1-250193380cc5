import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { ProjectUser } from './project-user.entity';
import { ProjectVersion } from './project-version.entity';
import { Project } from './project.entity';
import { ProjectsController } from './projects.controller';
import { ProjectsService } from './projects.service';
import { clerkClientProviders } from '../../common/providers/clerk-client.providers';
import { GithubService } from '../../common/services/github/github.service';
import { Message } from '../messages/message.entity';
import { MessagesModule } from '../messages/messages.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Project, ProjectVersion, ProjectUser, Message]),
    forwardRef(() => MessagesModule),
    forwardRef(() => QueueModule),
  ],
  exports: [ProjectsService],
  controllers: [ProjectsController],
  providers: [...clerkClientProviders, ProjectsService, GithubService],
})
export class ProjectsModule {}
