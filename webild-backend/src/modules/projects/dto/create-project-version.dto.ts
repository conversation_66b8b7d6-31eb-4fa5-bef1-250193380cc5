import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateProjectVersionDto {
  @ApiProperty()
  declare version_prompt: string;
}

export class UpdateProjectVersionDto extends CreateProjectVersionDto {
  @ApiPropertyOptional({ nullable: true })
  declare feedback_info: string;

  @ApiPropertyOptional({ nullable: true })
  declare feedback_score: number;
}

export class CreateProjectVersionDtoResponse {
  @ApiProperty()
  declare id: string;

  @ApiProperty()
  declare project_id: string;

  @ApiProperty()
  declare version_prompt: string | null;

  @ApiProperty()
  declare version_branch: string;

  @ApiProperty()
  declare version_status: string;

  @ApiProperty({ nullable: true })
  declare feedback_info: string | null;

  @ApiProperty({ nullable: true })
  declare feedback_score: number | null;

  @ApiProperty()
  declare createdAt: Date;

  @ApiProperty()
  declare updatedAt: Date;
}
