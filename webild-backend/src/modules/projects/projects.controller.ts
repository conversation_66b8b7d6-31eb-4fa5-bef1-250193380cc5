import { Body, Controller, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';

import { CreateProjectVersionDto, UpdateProjectVersionDto } from './dto/create-project-version.dto';
import { CreateProjectDto } from './dto/create-project.dto';
import { Project } from './project.entity';
import { ProjectsService } from './projects.service';
import {
  SwaggerCreateProject,
  SwaggerCreateProjectVersion,
  SwaggerExecuteProjectVersion,
  SwaggerGetProjectById,
  SwaggerGetProjects,
  SwaggerUpdateProject,
  SwaggerUpdateProjectVersion,
} from './projects.swagger';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';
import {
  CursorPaginationQueryDto,
  CursorPaginationResponseDto,
} from '../../common/dto/cursor-pagination.dto';
import { ProjectOwnershipGuard } from '../../common/guards/project-ownership.guard';
import { errorResponse, successResponse } from '../../common/utils/response.util';
import { User } from '../users/users.entity';
import { UpdateProjectDto } from './dto/update-project.dto';

@ApiBearerAuth('clerk-auth')
@Controller('projects')
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @SwaggerGetProjects()
  @Get()
  async getUserProjects(@DbAuthUser() dbUser: User, @Query() query: CursorPaginationQueryDto) {
    try {
      const { data, meta } = await this.projectsService.getUserProjects({
        userId: dbUser.id,
        limit: query.limit ? parseInt(`${query.limit}` || '10', 10) : 10,
        cursor: query.cursor || undefined,
      });

      return successResponse<CursorPaginationResponseDto<Project> | null>(
        data && meta ? { data, meta } : null,
      );
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerGetProjectById()
  @UseGuards(ProjectOwnershipGuard)
  @Get(':projectId')
  async getProject(@Param('projectId') projectId: string) {
    try {
      const project = await this.projectsService.getProject({ id: projectId });
      return successResponse({ data: project }, 200);
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerCreateProject()
  @Post()
  async createProject(@DbAuthUser() dbUser: User, @Body() dto: CreateProjectDto) {
    try {
      const project = await this.projectsService.createProject(dto, dbUser.id);
      return successResponse({ data: project }, 201);
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerUpdateProject()
  @UseGuards(ProjectOwnershipGuard)
  @Put(':projectId')
  async updateProject(@Param('projectId') projectId: string, @Body() dto: UpdateProjectDto) {
    try {
      const project = await this.projectsService.updateProject(projectId, dto);
      return successResponse({ data: project });
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerCreateProjectVersion()
  @UseGuards(ProjectOwnershipGuard)
  @Post(':projectId/versions')
  async createProjectVersion(
    @DbAuthUser() dbUser: User,
    @Param('projectId') projectId: string,
    @Body() dto: CreateProjectVersionDto,
  ) {
    try {
      const projectVersion = await this.projectsService.createProjectVersion({
        ...dto,
        project_id: projectId,
      });
      return successResponse({ data: projectVersion }, 201);
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerUpdateProjectVersion()
  @UseGuards(ProjectOwnershipGuard)
  @Put(':projectId/versions/:versionId')
  async updateProjectVersion(
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string,
    @Body() dto: UpdateProjectVersionDto,
  ) {
    try {
      const version = await this.projectsService.updateProjectVersion(projectId, versionId, dto);
      return successResponse({ data: version });
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerExecuteProjectVersion()
  @UseGuards(ProjectOwnershipGuard)
  @Post(':projectId/versions/:versionId/execute')
  async executeProjectVersion(
    @DbAuthUser() user: User,
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string,
  ) {
    try {
      const updatedVersion = await this.projectsService.executeVersionPrompt(
        projectId,
        versionId,
        user.id,
      );
      return successResponse({ data: updatedVersion });
    } catch (e) {
      return errorResponse(e, 500);
    }
  }
}
