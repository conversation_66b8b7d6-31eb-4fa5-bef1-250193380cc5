import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Job } from 'bullmq';

import { SystemPrompts } from '../../../common/services/claude/claude.service';
import { GithubService } from '../../../common/services/github/github.service';
import { ProjectsService } from '../../projects/projects.service';
import { PROJECT_FILES_QUEUE } from '../queue.constants';

interface GenerateProjectFilesPayload {
  prompt: string;
  userId: string;
  projectId: string;
  versionId: string;
}

@Processor(PROJECT_FILES_QUEUE)
@Injectable()
export class GenerateProjectProcessor extends WorkerHost {
  constructor(
    private readonly projectsService: ProjectsService,
    private readonly gitHubService: GithubService,
  ) {
    super();
  }

  override async process(job: Job<GenerateProjectFilesPayload>): Promise<void> {
    const { prompt, userId, projectId, versionId } = job.data;

    type BuildResult = { success: true } | { success: false; error?: string };

    const runCiPipeline = async (
      owner: string,
      repo: string,
      branch: string,
    ): Promise<BuildResult> => {
      await this.gitHubService.ensureWorkflowIsIndexed(owner, repo);
      await this.gitHubService.triggerWorkflowDispatch(owner, repo, branch);
      return this.gitHubService.waitForWorkflowCompletion(owner, repo, branch);
    };

    const pushAll = async (owner: string, repo: string, branch: string) => {
      await this.projectsService.pushProjectToGitHub(projectId, versionId);
      await this.gitHubService.forcePushBranchToMain(owner, repo, branch);
    };

    const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

    try {
      const userPrompt = `Generate code based on the following requirements:\n\n${prompt}`;
      const initialFiles = await this.projectsService.getProjectStructure(
        userId,
        projectId,
        versionId,
        userPrompt,
        [SystemPrompts.GENERATE_CODE],
      );

      await this.projectsService.createProjectFromFiles(initialFiles, userId, projectId, versionId);

      const { repositoryOwner, repositoryName, branch } =
        await this.projectsService.pushProjectToGitHub(projectId, versionId);

      await this.gitHubService.forcePushBranchToMain(repositoryOwner, repositoryName, branch);

      const result = await runCiPipeline(repositoryOwner, repositoryName, branch);
      if (result.success) {
        // ToDo: add generate github pages
        console.log('result', result);
        return;
      }

      const MAX_FIX_ATTEMPTS = 5;
      let attempt = 1;
      let lastError = result.error ?? 'Unknown build error';

      while (attempt <= MAX_FIX_ATTEMPTS) {
        const fixPrompt = `Errors: ${lastError}`;
        const filesToChange = await this.projectsService.getProjectStructure(
          userId,
          projectId,
          versionId,
          fixPrompt,
          [SystemPrompts.FIX_CODE_ERRORS],
        );

        if (!filesToChange || filesToChange.length === 0) {
          console.warn('No files suggested for change. Stopping auto-fix loop.');
          break;
        }

        await this.projectsService.createProjectFromFiles(
          filesToChange,
          userId,
          projectId,
          versionId,
          false,
        );

        await pushAll(repositoryOwner, repositoryName, branch);

        await sleep(1500 + attempt * 250);

        const next = await runCiPipeline(repositoryOwner, repositoryName, branch);
        if (next.success) {
          console.log('result', next);
          return;
        }

        lastError = next.error ?? lastError;
        console.log(`Attempt ${attempt} failed:`, lastError);
        attempt++;

        await sleep(1000 * attempt);
      }
      // ToDo: return some error to the frontend
      console.error('Failed after auto-fix attempts. Last error:', lastError);
    } catch (error) {
      console.error('Failed to create project files', error);
      throw new Error('Failed to create project files');
    }
  }
}
