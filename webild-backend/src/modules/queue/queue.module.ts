import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullModule } from '@nestjs/bullmq';
import { forwardRef, Module } from '@nestjs/common';

import { PROJECT_FILES_QUEUE, TRANSCRIBE_QUEUE } from './queue.constants';
import { QueueService } from './queue.service';
import { GithubService } from '../../common/services/github/github.service';
import { MessagesModule } from '../messages/messages.module';
import { ProjectsModule } from '../projects/projects.module';
import { GenerateProjectProcessor } from './processors/generate-project.processor';

@Module({
  imports: [
    BullModule.registerQueue({
      name: PROJECT_FILES_QUEUE,
    }),
    BullModule.registerQueue({
      name: TRANSCRIBE_QUEUE,
      defaultJobOptions: {
        attempts: 3,
        removeOnComplete: 50,
        removeOnFail: 10,
      },
    }),
    BullBoardModule.forFeature({
      name: PROJECT_FILES_QUEUE,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: TRANSCRIBE_QUEUE,
      adapter: BullMQAdapter,
    }),
    forwardRef(() => MessagesModule),
    forwardRef(() => ProjectsModule),
  ],
  providers: [QueueService, GenerateProjectProcessor, GithubService],
  exports: [QueueService, BullModule],
})
export class QueueModule {}
