import { InjectQueue } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Queue } from 'bullmq';

import { PROJECT_FILES_PROCESS, PROJECT_FILES_QUEUE } from './queue.constants';

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue(PROJECT_FILES_QUEUE)
    private readonly queue: Queue,
  ) {}

  async enqueueProjectGenerationJob(
    prompt: string,
    userId: string,
    projectId: string,
    versionId: string,
  ): Promise<void> {
    await this.queue.add(PROJECT_FILES_PROCESS, {
      prompt,
      userId,
      projectId,
      versionId,
    });
  }
}
