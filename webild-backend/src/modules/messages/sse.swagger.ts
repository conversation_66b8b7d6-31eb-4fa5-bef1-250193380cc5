import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

export function SwaggerSubscribeToUserMessages() {
  return applyDecorators(
    ApiOperation({
      summary: 'Subscribe to SSE user message notifications',
      description:
        'Establishes an SSE connection to receive notifications about new messages from all projects where the user is a member.',
    }),
    ApiResponse({
      status: 200,
      description: 'SSE connection established',
      content: {
        'text/event-stream': {
          schema: {
            type: 'string',
            example: 'data: {"type":"connection","data":{"status":"connected"}}\n\n',
          },
        },
      },
    }),
  );
}
