import { Controller, Get, Res, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

import { SwaggerSubscribeToUserMessages } from './sse.swagger';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';
import { ClerkAuthGuard } from '../../common/guards/clerk-auth.guard';
import { SSEService } from '../../common/services/sse/sse.service';
import { User } from '../users/users.entity';

@ApiTags('SSE')
@ApiBearerAuth('clerk-auth')
@Controller('sse')
@UseGuards(ClerkAuthGuard)
export class SSEController {
  constructor(private readonly sseService: SSEService) {}

  @SwaggerSubscribeToUserMessages()
  @Get('messages')
  async subscribeToUserMessages(
    @DbAuthUser() user: User,
    @Res({ passthrough: true }) response: Response,
  ) {
    this.sseService.addClient(user.id, response);

    return {};
  }
}
