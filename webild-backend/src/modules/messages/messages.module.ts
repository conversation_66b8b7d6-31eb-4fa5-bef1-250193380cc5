import { forwardRef, Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { Message } from './message.entity';
import { MessagesController } from './messages.controller';
import { MessagesService } from './messages.service';
import { SSEController } from './sse.controller';
import { clerkClientProviders } from '../../common/providers/clerk-client.providers';
import { ClaudeService } from '../../common/services/claude/claude.service';
import { SSEService } from '../../common/services/sse/sse.service';
import { ProjectUser } from '../projects/project-user.entity';
import { ProjectsModule } from '../projects/projects.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Message, ProjectUser]),
    forwardRef(() => ProjectsModule),
    UsersModule,
  ],
  providers: [...clerkClientProviders, MessagesService, ClaudeService, SSEService],
  exports: [MessagesService, SSEService],
  controllers: [MessagesController, SSEController],
})
export class MessagesModule {}
