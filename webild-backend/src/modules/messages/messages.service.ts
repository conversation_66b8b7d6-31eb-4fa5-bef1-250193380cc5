import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

import { CreateMessageDto } from './dto/create-message.dto';
import { MessageTypes } from './message.constants';
import { Message } from './message.entity';
import {
  CursorPaginationQueryDto,
  CursorPaginationResponseDto,
} from '../../common/dto/cursor-pagination.dto';
import { ClaudeService, SystemPrompts } from '../../common/services/claude/claude.service';
import { welcomePrompt } from '../../common/services/claude/prompts/welcomePrompt';
import { paginateCursor } from '../../common/utils/paginate-cursor.util';
import { ProjectsService } from '../projects/projects.service';
import { GetMessageDto } from './dto/get-message.dto';
import { SSEService } from '../../common/services/sse/sse.service';

@Injectable()
export class MessagesService {
  constructor(
    @InjectModel(Message) private readonly messageModel: typeof Message,
    private readonly claudeService: ClaudeService,
    @Inject(forwardRef(() => ProjectsService))
    private readonly projectsService: ProjectsService,
    private readonly sseService: SSEService,
  ) {}

  async create(
    userId: string,
    projectId: string,
    versionId: string,
    dto: CreateMessageDto,
  ): Promise<Message> {
    const projectVersion = await this.projectsService.getProjectVersion(projectId, versionId);

    const message = await this.messageModel.create({
      ...dto,
      user_id: userId,
      project_id: projectId,
      version_id: versionId,
      version_number: projectVersion.version_number,
    });

    this.sseService.sendToUser(userId, {
      type: 'message_created',
      data: {
        id: message.id,
        project_id: message.project_id,
        version_id: message.version_id,
        version_number: message.version_number,
        type: message.type,
        message: message.message,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
      },
    });

    return message;
  }

  async askNStoreAI(
    userId: string,
    projectId: string,
    versionId: string,
    dto: CreateMessageDto,
    systemPrompts: SystemPrompts[] = [],
    saveMessages: boolean = true,
  ): Promise<Message> {
    const historyMessages = await this.getHistoryMessages(projectId, versionId);

    let userMessage: Message | null = null;
    let aiMessage: Message;
    if (saveMessages) {
      userMessage = await this.create(userId, projectId, versionId, {
        message: dto.message,
        type: MessageTypes.User,
      });
    }

    const aiAnswer = await this.claudeService.sendMessage(
      userMessage?.message || dto.message,
      systemPrompts,
      historyMessages,
    );

    if (saveMessages) {
      aiMessage = await this.create(userId, projectId, versionId, {
        message: aiAnswer,
        type: MessageTypes.AI,
      });
    } else {
      aiMessage = { message: aiAnswer, type: MessageTypes.AI } as Message;
    }

    return aiMessage;
  }

  async initAIDialogue(userId: string, projectId: string, versionId: string): Promise<Message> {
    const aiAnswer = await this.claudeService.sendMessage('', [SystemPrompts.WELCOME]);
    return await this.create(userId, projectId, versionId, {
      message: aiAnswer,
      type: MessageTypes.AI,
    });
  }

  async getHistoryMessages(projectId: string, versionId: string): Promise<Message[]> {
    const projectVersion = await this.projectsService.getProjectVersion(projectId, versionId);

    const messages = await this.messageModel.findAll({
      where: {
        project_id: projectId,
        version_number: { [Op.lte]: projectVersion.version_number },
      },
      order: [['created_at', 'ASC']],
    });

    return [{ type: MessageTypes.User, message: welcomePrompt } as Message, ...messages];
  }

  async generateVersionPrompt(projectId: string, versionId: string): Promise<string> {
    const historyMessages = await this.getHistoryMessages(projectId, versionId);

    return await this.claudeService.sendMessage('', [SystemPrompts.OPTIMIZE], historyMessages);
  }

  async getPaginatedMessages(props: {
    projectId: string;
    versionId?: string;
    query: CursorPaginationQueryDto;
  }): Promise<CursorPaginationResponseDto<GetMessageDto>> {
    const { projectId, versionId, query } = props;

    const { data, meta } = await paginateCursor(Message, {
      limit: query.limit ?? 10,
      cursor: query.cursor,
      where: {
        project_id: projectId,
        ...(versionId ? { version_id: versionId } : {}),
      },
      orderBy: 'created_at',
      orderDirection: 'DESC',
    });

    const mapped = data.map((msg) => ({
      id: msg.id,
      version_number: msg.version_number,
      type: msg.type,
      message: msg.message,
      createdAt: msg.createdAt,
      updatedAt: msg.updatedAt,
    }));

    return {
      data: mapped,
      meta,
    };
  }
}
