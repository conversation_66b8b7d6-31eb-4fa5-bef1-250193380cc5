import { ApiProperty } from '@nestjs/swagger';

import { MessageTypes } from '../message.constants';

export class CreateMessageDto {
  @ApiProperty({ enum: MessageTypes })
  declare type: MessageTypes;

  @ApiProperty({ example: 'Hello, assistant!' })
  declare message: string;
}

export class MessageResponseDto {
  @ApiProperty()
  declare id: string;

  @ApiProperty()
  declare project_id: string;

  @ApiProperty()
  declare version_id: string;

  @ApiProperty()
  declare user_id: string;

  @ApiProperty({ enum: MessageTypes })
  declare type: MessageTypes;

  @ApiProperty()
  declare message: string;

  @ApiProperty()
  declare createdAt: Date;

  @ApiProperty()
  declare updatedAt: Date;
}
