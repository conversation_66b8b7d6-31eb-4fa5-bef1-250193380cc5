init:
	@echo "🧹 Cleaning..."
	rm -rf node_modules
	rm -f package-lock.json
	npm install
	@if [ ! -f .env ]; then cp .env.example .env; fi

	@echo "🐳 Starting Docker containers..."
	cd docker && docker compose --env-file ../.env up -d --build

	@echo "🧼 Resetting database migrations..."
	npx sequelize-cli db:migrate:undo:all
	npx sequelize-cli db:migrate

	@echo "✅ Done."

start:
	cd docker && docker compose --env-file ../.env up -d
	npm run start:dev
